-- Database Schema for Learning Platform

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (handled by Supa<PERSON> Auth)
-- This table is managed by Supabase Auth and contains basic user authentication info
-- Note: We'll reference this table but not create it as it's managed by Supabase

-- Profiles table - Primary users table with essential info
create table public.profiles (
  id uuid not null,
  first_name text null,
  last_name text null,
  user_type text not null default 'user'::text,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  profile_picture_url text null,
  email text not null,
  timezone text null,
  constraint profiles_pkey primary key (id),
  constraint profiles_email_unique unique (email),
  constraint profiles_id_fkey foreign KEY (id) references auth.users (id),
  constraint profiles_user_type_check check (
    (
      user_type = any (
        array[
          'student'::text,
          'tutor'::text,
          'admin'::text,
          'user'::text
        ]
      )
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_profiles_picture_url on public.profiles using btree (profile_picture_url) TABLESPACE pg_default;

create index IF not exists idx_profiles_user_type on public.profiles using btree (user_type) TABLESPACE pg_default
where
  (user_type is not null);

create trigger refresh_on_profile_change
after INSERT
or DELETE
or
update on profiles for EACH STATEMENT
execute FUNCTION trigger_refresh_student_profile ();

create trigger sync_user_type_trigger
after INSERT
or
update OF user_type on profiles for EACH row
execute FUNCTION sync_user_type_to_jwt ();

create trigger timezone_notification_trigger
after INSERT
or
update OF timezone on profiles for EACH row
execute FUNCTION handle_timezone_notification ();

create trigger update_profiles_timestamp BEFORE
update on profiles for EACH row
execute FUNCTION update_timestamp ();

-- Subjects table - Main subject areas
create table public.subjects (
  id uuid not null default extensions.uuid_generate_v4 (),
  name text not null,
  description text null,
  icon text null,
  color text null,
  is_active boolean null default true,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  constraint subjects_pkey primary key (id),
  constraint subjects_name_key unique (name)
) TABLESPACE pg_default;

create index IF not exists subjects_is_active_idx on public.subjects using btree (is_active) TABLESPACE pg_default;

create trigger update_subjects_timestamp BEFORE
update on subjects for EACH row
execute FUNCTION update_timestamp ();

-- Topics table - Categories within subjects
create table public.topics (
  id uuid not null default extensions.uuid_generate_v4 (),
  subject_id uuid not null,
  name text not null,
  description text null,
  icon text null,
  display_order integer null,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  constraint topics_pkey primary key (id),
  constraint topics_subject_id_name_key unique (subject_id, name),
  constraint topics_subject_id_fkey foreign KEY (subject_id) references subjects (id)
) TABLESPACE pg_default;

create trigger update_topics_timestamp BEFORE
update on topics for EACH row
execute FUNCTION update_timestamp ();

-- Subtopics table - Specific learning units within topics (also called standards)
create table public.subtopics (
  id uuid not null default extensions.uuid_generate_v4 (),
  topic_id uuid not null,
  name text not null,
  description text null,
  state_standard text null,
  display_order integer null,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  constraint subtopics_pkey primary key (id),
  constraint subtopics_topic_id_name_key unique (topic_id, name),
  constraint subtopics_topic_id_fkey foreign KEY (topic_id) references topics (id)
) TABLESPACE pg_default;

create trigger update_subtopics_timestamp BEFORE
update on subtopics for EACH row
execute FUNCTION update_timestamp ();

-- Resources table - Learning materials associated with subtopics
create table public.resources (
  id uuid not null default extensions.uuid_generate_v4 (),
  subtopic_id uuid not null,
  name text not null,
  type text not null,
  url text null,
  content text null,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  constraint resources_pkey primary key (id),
  constraint resources_subtopic_id_fkey foreign KEY (subtopic_id) references subtopics (id),
  constraint resources_type_check check (
    (
      type = any (
        array[
          'document'::text,
          'video'::text,
          'quiz'::text,
          'exercise'::text,
          'other'::text
        ]
      )
    )
  )
) TABLESPACE pg_default;

create trigger update_resources_timestamp BEFORE
update on resources for EACH row
execute FUNCTION update_timestamp ();

-- products table - students may choose different available products
create table public.products (
  id uuid not null default extensions.uuid_generate_v4 (),
  name text not null,
  description text null,
  price numeric(10, 2) not null,
  duration_days integer not null,
  type text not null,
  features jsonb null,
  is_active boolean null default true,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  subject_id uuid null,
  constraint products_pkey primary key (id),
  constraint fk_subject foreign KEY (subject_id) references subjects (id) on delete CASCADE,
  constraint products_type_check check (
    (
      type = any (
        array[
          'booster'::text,
          'custom'::text,
          'preparation'::text
        ]
      )
    )
  )
) TABLESPACE pg_default;

-- Batches table - Student assignment to batch that relates students to assigned tutor and selected product
create table public.batches (
  id uuid not null default extensions.uuid_generate_v4 (),
  name text not null,
  student_id uuid not null,
  product_type text not null,
  product_name text not null,
  default_tutor_id uuid null,
  status text not null,
  start_date timestamp with time zone null,
  end_date timestamp with time zone null,
  total_sessions integer null,
  remaining_sessions integer null,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  product_id uuid null,
  subscription_id uuid null,
  constraint batches_pkey primary key (id),
  constraint batches_student_id_fkey foreign KEY (student_id) references profiles (id),
  constraint batches_subscription_id_fkey foreign KEY (subscription_id) references subscriptions (id),
  constraint batches_default_tutor_id_fkey foreign KEY (default_tutor_id) references profiles (id),
  constraint batches_product_id_fkey foreign KEY (product_id) references products (id),
  constraint valid_date_range check (
    (
      (end_date is null)
      or (start_date is null)
      or (end_date > start_date)
    )
  ),
  constraint batches_package_type_check check (
    (
      product_type = any (
        array[
          'booster'::text,
          'custom'::text,
          'preparation'::text
        ]
      )
    )
  ),
  constraint batches_status_check check (
    (
      status = any (
        array[
          'active'::text,
          'completed'::text,
          'paused'::text,
          'cancelled'::text
        ]
      )
    )
  )
) TABLESPACE pg_default;

create trigger check_batch_profile_roles BEFORE INSERT
or
update on batches for EACH row
execute FUNCTION validate_batch_profile_roles ();

create trigger update_batches_timestamp BEFORE
update on batches for EACH row
execute FUNCTION update_timestamp ();

-- Batch Topics table - Topics included in a batch with optional custom tutor assignment
create table public.batch_topics (
  id uuid not null default extensions.uuid_generate_v4 (),
  batch_id uuid not null,
  topic_id uuid not null,
  custom_tutor_id uuid null,
  status text not null,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  constraint batch_topics_pkey primary key (id),
  constraint batch_topics_batch_id_topic_id_key unique (batch_id, topic_id),
  constraint batch_topics_batch_id_fkey foreign KEY (batch_id) references batches (id),
  constraint batch_topics_custom_tutor_id_fkey foreign KEY (custom_tutor_id) references profiles (id),
  constraint batch_topics_topic_id_fkey foreign KEY (topic_id) references topics (id),
  constraint batch_topics_status_check check (
    (
      status = any (
        array[
          'not_started'::text,
          'in_progress'::text,
          'completed'::text
        ]
      )
    )
  )
) TABLESPACE pg_default;

create trigger update_batch_topics_timestamp BEFORE
update on batch_topics for EACH row
execute FUNCTION update_timestamp ();

-- Batch Subtopics table - Subtopics included in a batch with optional custom tutor assignment
create table public.batch_subtopics (
  id uuid not null default extensions.uuid_generate_v4 (),
  batch_topic_id uuid not null,
  subtopic_id uuid not null,
  custom_tutor_id uuid null,
  status text not null,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  constraint batch_subtopics_pkey primary key (id),
  constraint batch_subtopics_batch_topic_id_subtopic_id_key unique (batch_topic_id, subtopic_id),
  constraint batch_subtopics_batch_topic_id_fkey foreign KEY (batch_topic_id) references batch_topics (id),
  constraint batch_subtopics_custom_tutor_id_fkey foreign KEY (custom_tutor_id) references profiles (id),
  constraint batch_subtopics_subtopic_id_fkey foreign KEY (subtopic_id) references subtopics (id),
  constraint batch_subtopics_status_check check (
    (
      status = any (
        array[
          'not_started'::text,
          'in_progress'::text,
          'completed'::text
        ]
      )
    )
  )
) TABLESPACE pg_default;

create trigger update_batch_subtopics_timestamp BEFORE
update on batch_subtopics for EACH row
execute FUNCTION update_timestamp ();

-- Tutor Availability table - Tutor availability slots
create table public.tutor_availability (
  id uuid not null default extensions.uuid_generate_v4 (),
  tutor_id uuid not null,
  day_of_week integer not null,
  start_time time without time zone not null,
  end_time time without time zone not null,
  status text not null,
  created_at timestamp with time zone not null default timezone ('UTC'::text, now()),
  updated_at timestamp with time zone not null default timezone ('UTC'::text, now()),
  timezone text null,
  constraint tutor_availability_pkey primary key (id),
  constraint tutor_availability_tutor_id_fkey foreign KEY (tutor_id) references profiles (id) on delete CASCADE,
  constraint tutor_availability_day_of_week_check check (
    (
      (day_of_week >= 0)
      and (day_of_week <= 6)
    )
  ),
  constraint tutor_availability_status_check check (
    (
      status = any (
        array[
          'available'::text,
          'auto_accept'::text,
          'manual_approval'::text
        ]
      )
    )
  ),
  constraint valid_time_range check (
    (
      (end_time)::time with time zone > (start_time)::time with time zone
    )
  )
) TABLESPACE pg_default;

create trigger update_tutor_availability_timestamp BEFORE
update on tutor_availability for EACH row
execute FUNCTION update_timestamp ();

-- Tutor Auto Accept Rules table - Rules for automatic session acceptance
create table public.tutor_auto_accept_rules (
  id uuid not null default extensions.uuid_generate_v4 (),
  tutor_id uuid not null,
  name text not null,
  is_active boolean null default true,
  existing_students_only boolean null default false,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  constraint tutor_auto_accept_rules_pkey primary key (id),
  constraint tutor_auto_accept_rules_tutor_id_fkey foreign KEY (tutor_id) references profiles (id)
) TABLESPACE pg_default;

create trigger update_tutor_auto_accept_rules_timestamp BEFORE
update on tutor_auto_accept_rules for EACH row
execute FUNCTION update_timestamp ();

-- Rule Topics table - Topics that a rule applies to
create table public.rule_topics (
  id uuid not null default extensions.uuid_generate_v4 (),
  rule_id uuid not null,
  topic_id uuid not null,
  created_at timestamp with time zone not null default now(),
  constraint rule_topics_pkey primary key (id),
  constraint rule_topics_rule_id_topic_id_key unique (rule_id, topic_id),
  constraint rule_topics_rule_id_fkey foreign KEY (rule_id) references tutor_auto_accept_rules (id),
  constraint rule_topics_topic_id_fkey foreign KEY (topic_id) references topics (id)
) TABLESPACE pg_default;

-- Rule Time Ranges table - Time ranges that a rule applies to
create table public.rule_time_ranges (
  id uuid not null default extensions.uuid_generate_v4 (),
  rule_id uuid not null,
  day_of_week integer not null,
  start_time time without time zone null,
  end_time time without time zone null,
  created_at timestamp with time zone not null default now(),
  constraint rule_time_ranges_pkey primary key (id),
  constraint rule_time_ranges_rule_id_fkey foreign KEY (rule_id) references tutor_auto_accept_rules (id),
  constraint rule_time_ranges_day_of_week_check check (
    (
      (day_of_week >= 0)
      and (day_of_week <= 6)
    )
  ),
  constraint valid_time_range check (
    (
      (end_time is null)
      or (start_time is null)
      or (end_time > start_time)
    )
  )
) TABLESPACE pg_default;

-- Sessions table - Individual learning sessions
create table public.sessions (
  id uuid not null default extensions.uuid_generate_v4 (),
  batch_id uuid not null,
  topic_id uuid not null,
  subtopic_id uuid null,
  tutor_id uuid not null,
  student_id uuid not null,
  scheduled_at timestamp with time zone not null default timezone ('UTC'::text, now()),
  duration_min integer not null,
  status text not null,
  mode text not null,
  session_type text not null,
  meeting_url text null,
  location text null,
  has_conflict boolean null default false,
  urgency_level smallint null,
  rescheduled_from uuid null,
  created_by text not null,
  created_at timestamp with time zone not null default timezone ('UTC'::text, now()),
  updated_at timestamp with time zone not null default timezone ('UTC'::text, now()),
  meeting_provider_id uuid null,
  constraint sessions_pkey primary key (id),
  constraint sessions_subtopic_id_fkey foreign KEY (subtopic_id) references subtopics (id),
  constraint sessions_topic_id_fkey foreign KEY (topic_id) references topics (id),
  constraint sessions_tutor_id_fkey foreign KEY (tutor_id) references profiles (id),
  constraint sessions_student_id_fkey foreign KEY (student_id) references profiles (id),
  constraint sessions_rescheduled_from_fkey foreign KEY (rescheduled_from) references sessions (id),
  constraint sessions_meeting_provider_id_fkey foreign KEY (meeting_provider_id) references meeting_providers (id),
  constraint sessions_batch_id_fkey foreign KEY (batch_id) references batches (id),
  constraint sessions_urgency_level_check check (
    (
      (urgency_level >= 1)
      and (urgency_level <= 5)
    )
  ),
  constraint sessions_created_by_check check (
    (
      created_by = any (
        array['admin'::text, 'tutor'::text, 'student'::text]
      )
    )
  ),
  constraint sessions_mode_check check (
    (
      mode = any (
        array[
          'video'::text,
          'audio'::text,
          'quiz'::text,
          'hybrid'::text
        ]
      )
    )
  ),
  constraint sessions_session_type_check check (
    (
      session_type = any (
        array[
          'regular'::text,
          'demo'::text,
          'makeup'::text,
          'assessment'::text,
          'consultation'::text
        ]
      )
    )
  ),
  constraint sessions_status_check check (
    (
      status = any (
        array[
          'scheduled'::text,
          'completed'::text,
          'cancelled'::text,
          'rescheduled'::text
        ]
      )
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_sessions_meeting_provider on public.sessions using btree (meeting_provider_id) TABLESPACE pg_default;

create trigger update_sessions_timestamp BEFORE
update on sessions for EACH row
execute FUNCTION update_timestamp ();

-- Session Details table - Analytics and session metrics
create table public.session_details (
  session_id uuid not null,
  description text null,
  materials jsonb null,
  recording_url text null,
  student_attended boolean null default false,
  tutor_attended boolean null default false,
  start_time timestamp with time zone null default timezone ('UTC'::text, now()),
  end_time timestamp with time zone null default timezone ('UTC'::text, now()),
  actual_start_time timestamp with time zone null default timezone ('UTC'::text, now()),
  actual_end_time timestamp with time zone null default timezone ('UTC'::text, now()),
  tutor_talk_time integer null,
  student_talk_time integer null,
  whiteboard_interactions integer null,
  messages_sent integer null,
  messages_received integer null,
  participation_score numeric(3, 1) null,
  participation_level text null,
  cancellation_reason text null,
  notes text null,
  created_at timestamp with time zone not null default timezone ('UTC'::text, now()),
  updated_at timestamp with time zone not null default timezone ('UTC'::text, now()),
  meeting_metadata jsonb null,
  constraint session_details_pkey primary key (session_id),
  constraint session_details_session_id_fkey foreign KEY (session_id) references sessions (id),
  constraint valid_meeting_metadata check (validate_meeting_metadata (meeting_metadata))
) TABLESPACE pg_default;

create index IF not exists idx_session_details_meeting_metadata on public.session_details using gin (meeting_metadata) TABLESPACE pg_default;

create index IF not exists idx_session_details_meeting_provider on public.session_details using btree (((meeting_metadata ->> 'provider'::text))) TABLESPACE pg_default;

create index IF not exists idx_session_details_meeting_id on public.session_details using btree (((meeting_metadata ->> 'meeting_id'::text))) TABLESPACE pg_default;

create trigger update_session_details_timestamp BEFORE
update on session_details for EACH row
execute FUNCTION update_timestamp ();

-- Session Feedback table - Feedback for completed sessions
create table public.session_feedback (
  id uuid not null default extensions.uuid_generate_v4 (),
  session_id uuid not null,
  submitted_by uuid not null,
  user_role text not null,
  rating integer null,
  comments text null,
  areas_of_improvement text[] null,
  strengths text[] null,
  follow_up_requested boolean null default false,
  created_at timestamp with time zone not null default timezone ('UTC'::text, now()),
  updated_at timestamp with time zone not null default timezone ('UTC'::text, now()),
  constraint session_feedback_pkey primary key (id),
  constraint session_feedback_session_id_submitted_by_key unique (session_id, submitted_by),
  constraint session_feedback_session_id_fkey foreign KEY (session_id) references sessions (id),
  constraint session_feedback_submitted_by_fkey foreign KEY (submitted_by) references profiles (id),
  constraint session_feedback_rating_check check (
    (
      (rating >= 1)
      and (rating <= 5)
    )
  ),
  constraint session_feedback_user_role_check check (
    (
      user_role = any (array['student'::text, 'tutor'::text])
    )
  )
) TABLESPACE pg_default;

create trigger update_session_feedback_timestamp BEFORE
update on session_feedback for EACH row
execute FUNCTION update_timestamp ();


-- Session Requests table - Requests for new sessions
create table public.session_requests (
  id uuid not null default extensions.uuid_generate_v4 (),
  batch_id uuid not null,
  topic_id uuid not null,
  subtopic_id uuid null,
  tutor_id uuid null,
  student_id uuid not null,
  start_time timestamp with time zone not null default timezone ('UTC'::text, now()),
  end_time timestamp with time zone not null default timezone ('UTC'::text, now()),
  duration_min integer not null,
  mode text not null,
  location text null,
  notes text null,
  requested_by text not null,
  request_type text not null,
  original_session_id uuid null,
  status text not null,
  student_approval text null,
  tutor_approval text null,
  admin_approval text null,
  urgency text null,
  has_conflict boolean null default false,
  conflict_details jsonb null,
  auto_accepted boolean null default false,
  created_at timestamp with time zone not null default timezone ('UTC'::text, now()),
  updated_at timestamp with time zone not null default timezone ('UTC'::text, now()),
  constraint session_requests_pkey primary key (id),
  constraint session_requests_original_session_id_fkey foreign KEY (original_session_id) references sessions (id),
  constraint session_requests_tutor_id_fkey foreign KEY (tutor_id) references profiles (id),
  constraint session_requests_subtopic_id_fkey foreign KEY (subtopic_id) references subtopics (id),
  constraint session_requests_topic_id_fkey foreign KEY (topic_id) references topics (id),
  constraint session_requests_batch_id_fkey foreign KEY (batch_id) references batches (id),
  constraint session_requests_student_id_fkey foreign KEY (student_id) references profiles (id),
  constraint session_requests_tutor_approval_check check (
    (
      tutor_approval = any (
        array[
          'pending'::text,
          'approved'::text,
          'rejected'::text
        ]
      )
    )
  ),
  constraint session_requests_urgency_check check (
    (
      urgency = any (array['high'::text, 'medium'::text, 'low'::text])
    )
  ),
  constraint session_requests_admin_approval_check check (
    (
      admin_approval = any (
        array[
          'pending'::text,
          'approved'::text,
          'rejected'::text
        ]
      )
    )
  ),
  constraint session_requests_mode_check check (
    (
      mode = any (
        array[
          'video'::text,
          'audio'::text,
          'quiz'::text,
          'hybrid'::text
        ]
      )
    )
  ),
  constraint session_requests_request_type_check check (
    (
      request_type = any (
        array[
          'new'::text,
          'reschedule'::text,
          'cancellation'::text
        ]
      )
    )
  ),
  constraint session_requests_requested_by_check check (
    (
      requested_by = any (
        array['student'::text, 'tutor'::text, 'admin'::text]
      )
    )
  ),
  constraint session_requests_status_check check (
    (
      status = any (
        array[
          'pending'::text,
          'approved_by_tutor'::text,
          'approved_by_student'::text,
          'approved_by_admin'::text,
          'rejected_by_tutor'::text,
          'rejected_by_student'::text,
          'rejected_by_admin'::text,
          'cancelled'::text
        ]
      )
    )
  ),
  constraint session_requests_student_approval_check check (
    (
      student_approval = any (
        array[
          'pending'::text,
          'approved'::text,
          'rejected'::text
        ]
      )
    )
  ),
  constraint no_tutor_overlap EXCLUDE using gist (
    tutor_id
    with
      =,
      tstzrange (start_time, end_time)
    with
      &&
  ),
  constraint no_student_overlap EXCLUDE using gist (
    student_id
    with
      =,
      tstzrange (start_time, end_time)
    with
      &&
  )
) TABLESPACE pg_default;

create index IF not exists idx_session_requests_student_id on public.session_requests using btree (student_id) TABLESPACE pg_default;

create index IF not exists idx_session_requests_tutor_id on public.session_requests using btree (tutor_id) TABLESPACE pg_default;

create index IF not exists idx_session_requests_status on public.session_requests using btree (status) TABLESPACE pg_default;

create index IF not exists idx_session_requests_start_time on public.session_requests using btree (start_time) TABLESPACE pg_default;

create trigger update_session_requests_timestamp BEFORE
update on session_requests for EACH row
execute FUNCTION update_timestamp ();

-- Create a function to get the assigned tutor for a session
CREATE OR REPLACE FUNCTION get_assigned_tutor_for_session(
    p_batch_id UUID,
    p_topic_id UUID,
    p_subtopic_id UUID DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    tutor_id UUID;
    batch_topic_id UUID;
BEGIN
    -- If a subtopic is specified, check if there's a custom tutor at the subtopic level
    IF p_subtopic_id IS NOT NULL THEN
        -- Find the batch_topic_id first
        SELECT bt.id INTO batch_topic_id
        FROM batch_topics bt
        WHERE bt.batch_id = p_batch_id AND bt.topic_id = p_topic_id;

        -- Then check for a custom tutor at the subtopic level
        SELECT bs.custom_tutor_id INTO tutor_id
        FROM batch_subtopics bs
        WHERE bs.batch_topic_id = batch_topic_id AND bs.subtopic_id = p_subtopic_id;

        IF tutor_id IS NOT NULL THEN
            RETURN tutor_id;
        END IF;
    END IF;

    -- Check if there's a custom tutor at the topic level
    SELECT custom_tutor_id INTO tutor_id
    FROM batch_topics
    WHERE batch_id = p_batch_id AND topic_id = p_topic_id;

    IF tutor_id IS NOT NULL THEN
        RETURN tutor_id;
    END IF;

    -- Finally, fall back to the default tutor for the batch
    SELECT default_tutor_id INTO tutor_id
    FROM batches
    WHERE id = p_batch_id;

    RETURN tutor_id;
END;
$$ LANGUAGE plpgsql;

-- Note: This function determines the assigned tutor based on the hierarchy. The actual tutor assignment
-- for a session is stored directly in the sessions table and can be different
-- from the assigned tutor based on availability, admin decisions, etc.

-- Create triggers to update timestamps
CREATE OR REPLACE FUNCTION update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply the trigger to all tables with updated_at
CREATE TRIGGER update_profiles_timestamp BEFORE UPDATE ON profiles FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_subjects_timestamp BEFORE UPDATE ON subjects FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_topics_timestamp BEFORE UPDATE ON topics FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_subtopics_timestamp BEFORE UPDATE ON subtopics FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_resources_timestamp BEFORE UPDATE ON resources FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_batches_timestamp BEFORE UPDATE ON batches FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_batch_topics_timestamp BEFORE UPDATE ON batch_topics FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_batch_subtopics_timestamp BEFORE UPDATE ON batch_subtopics FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_sessions_timestamp BEFORE UPDATE ON sessions FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_session_requests_timestamp BEFORE UPDATE ON session_requests FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_session_details_timestamp BEFORE UPDATE ON session_details FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_session_feedback_timestamp BEFORE UPDATE ON session_feedback FOR EACH ROW EXECUTE FUNCTION update_timestamp();


-- meeting_sessions table
create table public.meeting_sessions (
  id uuid not null default extensions.uuid_generate_v4 (),
  session_id uuid not null,
  provider_id uuid not null,
  provider_meeting_id text null,
  meeting_url text not null,
  join_url text null,
  moderator_url text null,
  meeting_settings jsonb null,
  security_settings jsonb null,
  meeting_status text not null default 'scheduled'::text,
  scheduled_start_time timestamp with time zone null,
  scheduled_end_time timestamp with time zone null,
  actual_start_time timestamp with time zone null,
  actual_end_time timestamp with time zone null,
  recording_enabled boolean null default false,
  recording_url text null,
  recording_status text null,
  max_participants_reached integer null default 0,
  total_join_attempts integer null default 0,
  provider_response jsonb null,
  last_sync_at timestamp with time zone null,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  constraint meeting_sessions_pkey primary key (id),
  constraint meeting_sessions_session_id_key unique (session_id),
  constraint meeting_sessions_provider_id_fkey foreign KEY (provider_id) references meeting_providers (id),
  constraint meeting_sessions_session_id_fkey foreign KEY (session_id) references sessions (id),
  constraint meeting_sessions_meeting_status_check check (
    (
      meeting_status = any (
        array[
          'scheduled'::text,
          'started'::text,
          'ended'::text,
          'cancelled'::text,
          'failed'::text
        ]
      )
    )
  ),
  constraint meeting_sessions_recording_status_check check (
    (
      recording_status = any (
        array[
          'not_started'::text,
          'recording'::text,
          'processing'::text,
          'ready'::text,
          'failed'::text
        ]
      )
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_meeting_sessions_session_id on public.meeting_sessions using btree (session_id) TABLESPACE pg_default;

create index IF not exists idx_meeting_sessions_provider on public.meeting_sessions using btree (provider_id) TABLESPACE pg_default;

create index IF not exists idx_meeting_sessions_status on public.meeting_sessions using btree (meeting_status) TABLESPACE pg_default;

create index IF not exists idx_meeting_sessions_scheduled_start on public.meeting_sessions using btree (scheduled_start_time) TABLESPACE pg_default;

create trigger update_meeting_sessions_updated_at BEFORE
update on meeting_sessions for EACH row
execute FUNCTION update_meeting_updated_at_column ();


-- meeting_providers table
create table public.meeting_providers (
  id uuid not null default extensions.uuid_generate_v4 (),
  name text not null,
  display_name text not null,
  provider_type text not null,
  is_active boolean null default true,
  is_default boolean null default false,
  api_endpoint text null,
  webhook_url text null,
  oauth_config jsonb null,
  api_config jsonb null,
  supports_recording boolean null default false,
  supports_screen_sharing boolean null default false,
  supports_whiteboard boolean null default false,
  supports_breakout_rooms boolean null default false,
  supports_waiting_room boolean null default false,
  supports_chat boolean null default false,
  max_participants integer null,
  default_settings jsonb null,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  constraint meeting_providers_pkey primary key (id),
  constraint meeting_providers_name_key unique (name),
  constraint meeting_providers_provider_type_check check (
    (
      provider_type = any (
        array[
          'third_party'::text,
          'custom'::text,
          'embedded'::text
        ]
      )
    )
  ),
  constraint single_default_provider EXCLUDE using btree (
    is_default
    with
      =
  )
  where
    ((is_default = true))
) TABLESPACE pg_default;

create index IF not exists idx_meeting_providers_active on public.meeting_providers using btree (is_active) TABLESPACE pg_default;

create index IF not exists idx_meeting_providers_default on public.meeting_providers using btree (is_default) TABLESPACE pg_default;

create trigger update_meeting_providers_updated_at BEFORE
update on meeting_providers for EACH row
execute FUNCTION update_meeting_updated_at_column ();

-- meeting_participants table 
create table public.meeting_participants (
  id uuid not null default extensions.uuid_generate_v4 (),
  meeting_session_id uuid not null,
  user_id uuid not null,
  joined_at timestamp with time zone null,
  left_at timestamp with time zone null,
  duration_minutes integer null,
  participant_role text null,
  join_method text null,
  provider_participant_id text null,
  participant_data jsonb null,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  constraint meeting_participants_pkey primary key (id),
  constraint meeting_participants_meeting_session_id_fkey foreign KEY (meeting_session_id) references meeting_sessions (id),
  constraint meeting_participants_user_id_fkey foreign KEY (user_id) references profiles (id),
  constraint meeting_participants_join_method_check check (
    (
      join_method = any (
        array[
          'web'::text,
          'mobile'::text,
          'desktop'::text,
          'phone'::text,
          'api'::text
        ]
      )
    )
  ),
  constraint meeting_participants_participant_role_check check (
    (
      participant_role = any (
        array[
          'host'::text,
          'moderator'::text,
          'participant'::text,
          'observer'::text
        ]
      )
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_meeting_participants_meeting on public.meeting_participants using btree (meeting_session_id) TABLESPACE pg_default;

create index IF not exists idx_meeting_participants_user on public.meeting_participants using btree (user_id) TABLESPACE pg_default;

create index IF not exists idx_meeting_participants_joined_at on public.meeting_participants using btree (joined_at) TABLESPACE pg_default;

create trigger update_meeting_participants_updated_at BEFORE
update on meeting_participants for EACH row
execute FUNCTION update_meeting_updated_at_column ();


-- meeting_events table
create table public.meeting_events (
  id uuid not null default extensions.uuid_generate_v4 (),
  meeting_session_id uuid not null,
  user_id uuid null,
  event_type text not null,
  event_data jsonb null,
  provider_event_id text null,
  provider_timestamp timestamp with time zone null,
  created_at timestamp with time zone not null default now(),
  constraint meeting_events_pkey primary key (id),
  constraint meeting_events_meeting_session_id_fkey foreign KEY (meeting_session_id) references meeting_sessions (id),
  constraint meeting_events_user_id_fkey foreign KEY (user_id) references profiles (id),
  constraint meeting_events_event_type_check check (
    (
      event_type = any (
        array[
          'meeting_created'::text,
          'meeting_started'::text,
          'meeting_ended'::text,
          'meeting_cancelled'::text,
          'participant_joined'::text,
          'participant_left'::text,
          'recording_started'::text,
          'recording_stopped'::text,
          'screen_share_started'::text,
          'screen_share_stopped'::text,
          'chat_message'::text,
          'error'::text
        ]
      )
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_meeting_events_meeting on public.meeting_events using btree (meeting_session_id) TABLESPACE pg_default;

create index IF not exists idx_meeting_events_type on public.meeting_events using btree (event_type) TABLESPACE pg_default;

create index IF not exists idx_meeting_events_created_at on public.meeting_events using btree (created_at) TABLESPACE pg_default;
