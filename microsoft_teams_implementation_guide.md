# Microsoft Teams Backend Integration Implementation Guide

## 🎯 Overview

This guide provides step-by-step instructions for implementing Microsoft Teams integration using **Supabase Edge Functions** for secure, server-side authentication and meeting management. This backend approach provides better security and user experience compared to client-side implementations.

## 🏗️ Architecture

```
Frontend (React) → Supabase Edge Functions → Microsoft Graph API
                ↓
            Database (user_integrations table)
```

### Key Benefits
- ✅ **Secure**: Tokens stored server-side, never exposed to frontend
- ✅ **Better UX**: No popup windows or CORS issues  
- ✅ **Production Ready**: Proper token refresh and error handling
- ✅ **Maintainable**: Clean separation of concerns

## 📋 Prerequisites

Before starting the implementation, ensure you have:

1. **Azure Account**: Access to Azure portal with admin privileges
2. **Microsoft 365 Subscription**: Required for Teams functionality
3. **Supabase Project**: Your existing Supabase setup with CLI access
4. **Development Environment**: Node.js, npm, and your tutoring platform codebase

## Part 1: Azure App Registration Setup

### 1. Access Azure Portal
1. Navigate to [Azure Portal](https://portal.azure.com)
2. Sign in with your Azure account
3. Ensure you're in the correct tenant/directory

### 2. Navigate to App Registrations
1. In the Azure portal search bar, type: `App registrations`
2. Click on **App registrations** from the results
   - **Note:** The service is now under "Microsoft Entra ID" (formerly Azure Active Directory)
   - If you don't see it directly, you can also search for `Microsoft Entra ID` or `Azure Active Directory`
3. Click the **+ New registration** button

### 3. Configure Basic App Information
Fill out the registration form:

**Name:** `rfLearn Teams`

**Supported account types:** Select one of:
- ✅ **Accounts in this organizational directory only** (Single tenant) - *Recommended for most cases*
- ⚠️ **Accounts in any organizational directory** (Multi-tenant) - *Only if you need multi-tenant support*

**Redirect URI:**
- **Type:** Web
- **URL:** `https://your-supabase-project.supabase.co/functions/v1/teams-auth/callback`
- **Note:** Replace `your-supabase-project` with your actual Supabase project reference

Click **Register** to create the app.

### 4. Note Down Important IDs
After registration, you'll see the **Overview** page. Copy these values:

**Application (client) ID:** `xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`
→ This is your `AZURE_CLIENT_ID`

**Directory (tenant) ID:** `xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`
→ This is your `AZURE_TENANT_ID`

### 5. Create Client Secret
1. In the left sidebar, click **Certificates & secrets**
2. Click **+ New client secret**
3. **Description:** `rfLearn Teams Integration Secret`
4. **Expires:** Select `24 months` (recommended)
5. Click **Add**
6. **⚠️ IMPORTANT:** Copy the **Value** immediately (you can only see it once!)
   → This is your `AZURE_CLIENT_SECRET` (backend only)

### 6. Configure API Permissions
1. In the left sidebar, click **API permissions**
2. Click **+ Add a permission**
3. Select **Microsoft Graph**
4. Select **Application permissions**
5. Search for and add these permissions:
   - ✅ `OnlineMeetings.ReadWrite.All`
   - ✅ `User.Read.All`
   - ✅ `Calendars.ReadWrite.All` (optional, for calendar integration)
6. Click **Add permissions**
7. **⚠️ CRITICAL:** Click **Grant admin consent for [Your Organization]**
8. Confirm by clicking **Yes**

### 7. Verify Configuration
Go back to **Overview** and verify:
- ✅ Application ID is visible
- ✅ Directory ID is visible
- ✅ Redirect URI is configured correctly
- ✅ API permissions are granted with admin consent

## Part 2: Backend Implementation

### 1. Deploy Supabase Edge Functions

The backend implementation consists of two Edge Functions that are already created in your project:

- `supabase/functions/teams-auth/index.ts` - Handles OAuth flow
- `supabase/functions/teams-meetings/index.ts` - Creates meetings

**Deploy using the provided script:**

```bash
# Windows (PowerShell)
./scripts/deploy-teams-backend.ps1

# Linux/Mac
./scripts/deploy-teams-backend.sh
```

**Or deploy manually:**

```bash
# Deploy functions
supabase functions deploy teams-auth
supabase functions deploy teams-meetings

# Run database migration
supabase db push
```

### 2. Set Environment Variables

**No frontend environment variables needed!** All sensitive data is now handled server-side.

Set these in Supabase Edge Functions:

```bash
# Set in Supabase (keep secret)
supabase secrets set AZURE_CLIENT_ID=your_application_client_id_from_step_4
supabase secrets set AZURE_CLIENT_SECRET=your_client_secret_from_step_5
supabase secrets set AZURE_TENANT_ID=your_directory_tenant_id_from_step_4
supabase secrets set FRONTEND_URL=http://localhost:8080
```

### 3. Database Schema

The migration creates a `user_integrations` table to store OAuth tokens securely:

```sql
CREATE TABLE user_integrations (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id),
    provider TEXT CHECK (provider IN ('microsoft_teams', 'google_meet', 'zoom')),
    access_token TEXT NOT NULL,
    refresh_token TEXT,
    expires_at TIMESTAMPTZ NOT NULL,
    scope TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, provider)
);
```

## Part 3: Testing Your Setup

### 1. Deploy and Configure

1. **Deploy the backend functions:**
   ```bash
   ./scripts/deploy-teams-backend.ps1
   ```

2. **Update Azure redirect URI** to match your Supabase function URL

3. **Set environment variables** in Supabase

### 2. Test Authentication

1. **Start your development server:**
   ```bash
   npm run dev
   ```

2. **Test authentication:**
   - Navigate to any page with Teams integration (e.g., session management)
   - Click "Connect to Teams"
   - You should be redirected to Microsoft login
   - After successful login, you should be redirected back with `?teams_success=true`

### 3. Test Meeting Creation

1. **Try creating a test meeting** from a session page
2. **Verify that a Teams meeting URL is generated and stored**
3. **Check the database** to ensure tokens are stored in `user_integrations`

## 🔄 Authentication Flow

### Step 1: Initiate Authentication
```typescript
// User clicks "Connect to Teams"
const success = await teamsService.authenticate(currentUrl);
// User is redirected to Microsoft OAuth
```

### Step 2: OAuth Callback
```
Microsoft → Supabase Edge Function → Exchange code for tokens → Store in database → Redirect back to frontend
```

### Step 3: Success Handling
```typescript
// Frontend detects success from URL parameters
useEffect(() => {
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.get('teams_success') === 'true') {
    setIsAuthenticated(true);
    // Show success message
  }
}, []);
```

## 💻 Usage Examples

### Check Authentication Status
```typescript
const { isAuthenticated } = useTeamsIntegration();
```

### Authenticate User
```typescript
const { authenticate } = useTeamsIntegration();
await authenticate(); // Redirects to Microsoft, then back
```

### Create Meeting
```typescript
const { createAndStoreMeeting } = useTeamsIntegration();

const success = await createAndStoreMeeting(sessionId, {
  subject: 'Math Tutoring Session',
  startTime: '2024-12-08T10:00:00Z',
  endTime: '2024-12-08T11:00:00Z',
  participants: ['<EMAIL>', '<EMAIL>'],
});
```

## 🚨 Common Issues & Solutions

### Issue: "Redirect URI mismatch"
**Solution:** 
- Ensure Azure redirect URI exactly matches your Supabase function URL
- Format: `https://your-project.supabase.co/functions/v1/teams-auth/callback`

### Issue: "Permission denied"
**Solution:**
- Verify API permissions are granted with admin consent
- Check that all required permissions are added

### Issue: "Function deployment fails"
**Solution:**
- Ensure Supabase CLI is installed and logged in
- Check that you're in the project root directory
- Verify `supabase/config.toml` exists

### Issue: "Authentication fails silently"
**Solution:**
- Check Supabase function logs: `supabase functions logs teams-auth`
- Verify environment variables are set correctly
- Ensure user has proper Supabase authentication

## 🎉 Success Indicators

You'll know everything is working when:
- ✅ Authentication redirects to Microsoft and back successfully
- ✅ URL shows `?teams_success=true` after authentication
- ✅ Meeting creation generates valid Teams URLs
- ✅ Tokens are stored in `user_integrations` table
- ✅ No CORS or popup-related errors

## 📚 Additional Resources

- [TEAMS_BACKEND_IMPLEMENTATION.md](./TEAMS_BACKEND_IMPLEMENTATION.md) - Detailed technical documentation
- [AZURE_APP_REGISTRATION_WALKTHROUGH.md](./AZURE_APP_REGISTRATION_WALKTHROUGH.md) - Step-by-step Azure setup
- [Supabase Edge Functions Documentation](https://supabase.com/docs/guides/functions)
- [Microsoft Graph API Documentation](https://docs.microsoft.com/en-us/graph/)

---

## 🧪 Demo Data for Testing Teams Integration

### Demo Insert Queries

Use these SQL queries to create demo data for testing the Microsoft Teams integration:

```sql
-- =====================================================
-- 1. DEMO USERS AND PROFILES
-- =====================================================

-- Insert demo profiles (assuming auth.users already exist)
INSERT INTO profiles (id, first_name, last_name, user_type, email, timezone) VALUES
('demo-student-001', 'Emma', 'Wilson', 'student', '<EMAIL>', 'America/New_York'),
('demo-tutor-001', 'Dr. Sarah', 'Johnson', 'tutor', '<EMAIL>', 'America/Los_Angeles'),
('demo-admin-001', 'Michael', 'Chen', 'admin', '<EMAIL>', 'America/Chicago')
ON CONFLICT (id) DO UPDATE SET
  first_name = EXCLUDED.first_name,
  last_name = EXCLUDED.last_name,
  email = EXCLUDED.email,
  timezone = EXCLUDED.timezone;

-- =====================================================
-- 2. DEMO CURRICULUM DATA
-- =====================================================

-- Insert demo subject
INSERT INTO subjects (id, name, description, icon) VALUES
('demo-subject-math', 'Mathematics', 'Advanced Mathematics for High School', '📐')
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description;

-- Insert demo topic
INSERT INTO topics (id, subject_id, name, description, display_order) VALUES
('demo-topic-calculus', 'demo-subject-math', 'Calculus I', 'Introduction to Differential and Integral Calculus', 1)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description;

-- Insert demo subtopic
INSERT INTO subtopics (id, topic_id, name, description, state_standard, display_order) VALUES
('demo-subtopic-limits', 'demo-topic-calculus', 'Limits and Continuity', 'Understanding limits and continuous functions', 'MATH.CALC.1', 1)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description;

-- =====================================================
-- 3. DEMO BATCH AND SUBSCRIPTION
-- =====================================================

-- Insert demo batch
INSERT INTO batches (id, name, student_id, product_type, product_name, default_tutor_id, status, start_date, end_date, total_sessions, remaining_sessions) VALUES
('demo-batch-001', 'Emma\'s Calculus Package', 'demo-student-001', 'complete_booster', 'Calculus Mastery', 'demo-tutor-001', 'active',
 CURRENT_DATE, CURRENT_DATE + INTERVAL '3 months', 20, 18)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  status = EXCLUDED.status,
  remaining_sessions = EXCLUDED.remaining_sessions;

-- Insert batch topic
INSERT INTO batch_topics (id, batch_id, topic_id, custom_tutor_id, status) VALUES
('demo-batch-topic-001', 'demo-batch-001', 'demo-topic-calculus', NULL, 'in_progress')
ON CONFLICT (id) DO UPDATE SET
  status = EXCLUDED.status;

-- =====================================================
-- 4. MICROSOFT TEAMS INTEGRATION DATA
-- =====================================================

-- Insert Microsoft Teams provider (if not exists)
INSERT INTO meeting_providers (
    id,
    name,
    display_name,
    provider_type,
    is_active,
    is_default,
    supports_recording,
    supports_screen_sharing,
    supports_whiteboard,
    supports_chat,
    max_participants,
    default_settings
) VALUES (
    'provider-teams-001',
    'microsoft_teams',
    'Microsoft Teams',
    'third_party',
    true,
    true,
    true,
    true,
    true,
    true,
    300,
    '{
        "allowAnonymousUsers": false,
        "recordAutomatically": false,
        "lobbyBypassSettings": "organizationAndFederated",
        "allowMeetingChat": true,
        "allowTeamsCameraOn": true,
        "allowTeamsMicOn": true
    }'::jsonb
) ON CONFLICT (name) DO UPDATE SET
    is_active = EXCLUDED.is_active,
    is_default = EXCLUDED.is_default;

-- Insert demo user integration (OAuth tokens for tutor)
INSERT INTO user_integrations (
    id,
    user_id,
    provider,
    access_token,
    refresh_token,
    expires_at,
    scope
) VALUES (
    'integration-001',
    'demo-tutor-001',
    'microsoft_teams',
    'demo_access_token_' || extract(epoch from now())::text,
    'demo_refresh_token_' || extract(epoch from now())::text,
    NOW() + INTERVAL '1 hour',
    'https://graph.microsoft.com/OnlineMeetings.ReadWrite https://graph.microsoft.com/User.Read'
) ON CONFLICT (user_id, provider) DO UPDATE SET
    access_token = EXCLUDED.access_token,
    expires_at = EXCLUDED.expires_at;

-- =====================================================
-- 5. DEMO SESSIONS WITH TEAMS MEETINGS
-- =====================================================

-- Insert demo session
INSERT INTO sessions (
    id,
    batch_id,
    topic_id,
    subtopic_id,
    tutor_id,
    student_id,
    scheduled_at,
    duration_min,
    status,
    mode,
    session_type,
    meeting_url,
    meeting_provider_id,
    created_by
) VALUES (
    'demo-session-001',
    'demo-batch-001',
    'demo-topic-calculus',
    'demo-subtopic-limits',
    'demo-tutor-001',
    'demo-student-001',
    NOW() + INTERVAL '1 day',
    60,
    'scheduled',
    'video',
    'regular',
    'https://teams.microsoft.com/l/meetup-join/demo-meeting-url',
    'provider-teams-001',
    'tutor'
) ON CONFLICT (id) DO UPDATE SET
    scheduled_at = EXCLUDED.scheduled_at,
    meeting_url = EXCLUDED.meeting_url;

-- Insert demo meeting session
INSERT INTO meeting_sessions (
    id,
    session_id,
    provider_id,
    provider_meeting_id,
    meeting_url,
    join_url,
    meeting_settings,
    meeting_status,
    scheduled_start_time,
    scheduled_end_time,
    recording_enabled,
    provider_response
) VALUES (
    'demo-meeting-001',
    'demo-session-001',
    'provider-teams-001',
    'demo_teams_meeting_' || extract(epoch from now())::text,
    'https://teams.microsoft.com/l/meetup-join/demo-meeting-url',
    'https://teams.microsoft.com/l/meetup-join/demo-meeting-url',
    '{
        "allowAnonymousUsers": false,
        "recordAutomatically": false,
        "allowMeetingChat": true,
        "allowTeamsCameraOn": true,
        "allowTeamsMicOn": true
    }'::jsonb,
    'scheduled',
    NOW() + INTERVAL '1 day',
    NOW() + INTERVAL '1 day' + INTERVAL '1 hour',
    false,
    '{
        "id": "demo_teams_meeting_id",
        "joinWebUrl": "https://teams.microsoft.com/l/meetup-join/demo-meeting-url",
        "subject": "Calculus I - Limits and Continuity",
        "creationDateTime": "' || (NOW())::text || '"
    }'::jsonb
) ON CONFLICT (session_id) DO UPDATE SET
    meeting_url = EXCLUDED.meeting_url,
    scheduled_start_time = EXCLUDED.scheduled_start_time;

-- =====================================================
-- 6. DEMO MEETING PARTICIPANTS
-- =====================================================

-- Insert demo meeting participants
INSERT INTO meeting_participants (
    id,
    meeting_session_id,
    user_id,
    participant_role,
    join_method
) VALUES
(
    'demo-participant-001',
    'demo-meeting-001',
    'demo-tutor-001',
    'host',
    'web'
),
(
    'demo-participant-002',
    'demo-meeting-001',
    'demo-student-001',
    'participant',
    'web'
) ON CONFLICT (id) DO UPDATE SET
    participant_role = EXCLUDED.participant_role;

-- =====================================================
-- 7. DEMO USER MEETING PREFERENCES
-- =====================================================

-- Insert demo user meeting preferences
INSERT INTO user_meeting_preferences (
    id,
    user_id,
    preferred_provider_id,
    provider_settings,
    auto_join_enabled,
    recording_preference
) VALUES (
    'demo-pref-001',
    'demo-tutor-001',
    'provider-teams-001',
    '{
        "defaultRecording": false,
        "defaultMute": false,
        "defaultCamera": true
    }'::jsonb,
    true,
    'ask'
) ON CONFLICT (user_id, preferred_provider_id) DO UPDATE SET
    auto_join_enabled = EXCLUDED.auto_join_enabled;
```

### Testing Scenarios

After inserting the demo data, you can test these scenarios:

#### 1. **Authentication Test**
```sql
-- Verify user integration exists
SELECT * FROM user_integrations
WHERE user_id = 'demo-tutor-001' AND provider = 'microsoft_teams';
```

#### 2. **Meeting Creation Test**
```sql
-- Verify meeting session was created
SELECT
    s.id as session_id,
    s.meeting_url,
    ms.provider_meeting_id,
    ms.meeting_status,
    mp.display_name as provider_name
FROM sessions s
JOIN meeting_sessions ms ON s.id = ms.session_id
JOIN meeting_providers mp ON ms.provider_id = mp.id
WHERE s.id = 'demo-session-001';
```

#### 3. **Meeting Join Test**
```sql
-- Get meeting details for joining
SELECT
    s.id,
    s.scheduled_at,
    s.duration_min,
    ms.join_url,
    ms.meeting_settings,
    CONCAT(sp.first_name, ' ', sp.last_name) as student_name,
    CONCAT(tp.first_name, ' ', tp.last_name) as tutor_name
FROM sessions s
JOIN meeting_sessions ms ON s.id = ms.session_id
JOIN profiles sp ON s.student_id = sp.id
JOIN profiles tp ON s.tutor_id = tp.id
WHERE s.id = 'demo-session-001';
```

#### 4. **Participant Tracking Test**
```sql
-- View meeting participants
SELECT
    mp.user_id,
    CONCAT(p.first_name, ' ', p.last_name) as participant_name,
    mp.participant_role,
    mp.join_method,
    mp.joined_at,
    mp.left_at
FROM meeting_participants mp
JOIN profiles p ON mp.user_id = p.id
WHERE mp.meeting_session_id = 'demo-meeting-001';
```

### Cleanup Demo Data

To remove demo data after testing:

```sql
-- Remove demo data in reverse order of dependencies
DELETE FROM meeting_participants WHERE meeting_session_id = 'demo-meeting-001';
DELETE FROM meeting_sessions WHERE session_id = 'demo-session-001';
DELETE FROM sessions WHERE id = 'demo-session-001';
DELETE FROM user_integrations WHERE user_id IN ('demo-tutor-001', 'demo-student-001');
DELETE FROM user_meeting_preferences WHERE user_id IN ('demo-tutor-001', 'demo-student-001');
DELETE FROM batch_topics WHERE batch_id = 'demo-batch-001';
DELETE FROM batches WHERE id = 'demo-batch-001';
DELETE FROM subtopics WHERE id = 'demo-subtopic-limits';
DELETE FROM topics WHERE id = 'demo-topic-calculus';
DELETE FROM subjects WHERE id = 'demo-subject-math';
DELETE FROM profiles WHERE id IN ('demo-student-001', 'demo-tutor-001', 'demo-admin-001');
-- Note: Keep meeting_providers as they're system-wide configuration
```

---

**🎯 Next:** Once this setup is complete, your Teams integration will be fully functional with secure, server-side token management!
