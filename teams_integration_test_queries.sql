-- =====================================================
-- MICROSOFT TEAMS INTEGRATION TEST QUERIES
-- =====================================================
-- Use these queries to test the Teams integration after inserting demo data

-- =====================================================
-- 1. AUTHENTICATION TESTS
-- =====================================================

-- Verify user integration exists
SELECT 
    ui.id,
    ui.user_id,
    ui.provider,
    ui.expires_at,
    ui.scope,
    CONCAT(p.first_name, ' ', p.last_name) as user_name,
    p.user_type,
    CASE 
        WHEN ui.expires_at > NOW() THEN 'Valid'
        ELSE 'Expired'
    END as token_status
FROM user_integrations ui
JOIN profiles p ON ui.user_id = p.id
WHERE ui.user_id = 'demo-tutor-001' AND ui.provider = 'microsoft_teams';

-- Check all Teams integrations
SELECT 
    ui.user_id,
    CONCAT(p.first_name, ' ', p.last_name) as user_name,
    p.user_type,
    ui.provider,
    ui.created_at,
    ui.expires_at,
    CASE 
        WHEN ui.expires_at > NOW() THEN 'Valid'
        ELSE 'Expired'
    END as token_status
FROM user_integrations ui
JOIN profiles p ON ui.user_id = p.id
WHERE ui.provider = 'microsoft_teams'
ORDER BY ui.created_at DESC;

-- =====================================================
-- 2. MEETING PROVIDER TESTS
-- =====================================================

-- Verify Microsoft Teams provider configuration
SELECT 
    id,
    name,
    display_name,
    provider_type,
    is_active,
    is_default,
    supports_recording,
    supports_screen_sharing,
    supports_whiteboard,
    max_participants,
    default_settings
FROM meeting_providers 
WHERE name = 'microsoft_teams';

-- Check all active meeting providers
SELECT 
    name,
    display_name,
    is_active,
    is_default,
    max_participants
FROM meeting_providers 
WHERE is_active = true
ORDER BY is_default DESC, name;

-- =====================================================
-- 3. MEETING CREATION TESTS
-- =====================================================

-- Verify meeting session was created
SELECT 
    s.id as session_id,
    s.scheduled_at,
    s.duration_min,
    s.status as session_status,
    s.meeting_url,
    ms.provider_meeting_id,
    ms.meeting_status,
    ms.recording_enabled,
    mp.display_name as provider_name,
    CONCAT(st.first_name, ' ', st.last_name) as student_name,
    CONCAT(t.first_name, ' ', t.last_name) as tutor_name,
    top.name as topic_name,
    sub.name as subtopic_name
FROM sessions s
JOIN meeting_sessions ms ON s.id = ms.session_id
JOIN meeting_providers mp ON ms.provider_id = mp.id
JOIN profiles st ON s.student_id = st.id
JOIN profiles t ON s.tutor_id = t.id
JOIN topics top ON s.topic_id = top.id
LEFT JOIN subtopics sub ON s.subtopic_id = sub.id
WHERE s.id = 'demo-session-001';

-- Get all upcoming Teams meetings
SELECT 
    s.id,
    s.scheduled_at,
    s.duration_min,
    ms.meeting_url,
    ms.meeting_status,
    CONCAT(st.first_name, ' ', st.last_name) as student_name,
    CONCAT(t.first_name, ' ', t.last_name) as tutor_name,
    top.name as topic_name
FROM sessions s
JOIN meeting_sessions ms ON s.id = ms.session_id
JOIN meeting_providers mp ON ms.provider_id = mp.id
JOIN profiles st ON s.student_id = st.id
JOIN profiles t ON s.tutor_id = t.id
JOIN topics top ON s.topic_id = top.id
WHERE mp.name = 'microsoft_teams'
AND s.scheduled_at > NOW()
AND s.status = 'scheduled'
ORDER BY s.scheduled_at;

-- =====================================================
-- 4. MEETING JOIN TESTS
-- =====================================================

-- Get meeting details for joining (student perspective)
SELECT 
    s.id as session_id,
    s.scheduled_at,
    s.duration_min,
    s.status,
    ms.join_url,
    ms.meeting_settings,
    ms.meeting_status,
    CONCAT(t.first_name, ' ', t.last_name) as tutor_name,
    t.email as tutor_email,
    top.name as topic_name,
    sub.name as subtopic_name,
    b.name as batch_name
FROM sessions s
JOIN meeting_sessions ms ON s.id = ms.session_id
JOIN profiles t ON s.tutor_id = t.id
JOIN topics top ON s.topic_id = top.id
LEFT JOIN subtopics sub ON s.subtopic_id = sub.id
JOIN batches b ON s.batch_id = b.id
WHERE s.student_id = 'demo-student-001'
AND s.id = 'demo-session-001';

-- Get meeting details for joining (tutor perspective)
SELECT 
    s.id as session_id,
    s.scheduled_at,
    s.duration_min,
    s.status,
    ms.join_url,
    ms.meeting_settings,
    ms.meeting_status,
    CONCAT(st.first_name, ' ', st.last_name) as student_name,
    st.email as student_email,
    top.name as topic_name,
    sub.name as subtopic_name,
    b.name as batch_name
FROM sessions s
JOIN meeting_sessions ms ON s.id = ms.session_id
JOIN profiles st ON s.student_id = st.id
JOIN topics top ON s.topic_id = top.id
LEFT JOIN subtopics sub ON s.subtopic_id = sub.id
JOIN batches b ON s.batch_id = b.id
WHERE s.tutor_id = 'demo-tutor-001'
AND s.id = 'demo-session-001';

-- =====================================================
-- 5. PARTICIPANT TRACKING TESTS
-- =====================================================

-- View meeting participants
SELECT 
    mp.user_id,
    CONCAT(p.first_name, ' ', p.last_name) as participant_name,
    p.user_type,
    mp.participant_role,
    mp.join_method,
    mp.joined_at,
    mp.left_at,
    mp.duration_minutes
FROM meeting_participants mp
JOIN profiles p ON mp.user_id = p.id
WHERE mp.meeting_session_id = 'demo-meeting-001'
ORDER BY mp.participant_role, p.user_type;

-- Check participant status for a session
SELECT 
    s.id as session_id,
    s.scheduled_at,
    COUNT(mp.id) as total_participants,
    COUNT(CASE WHEN mp.joined_at IS NOT NULL THEN 1 END) as joined_participants,
    COUNT(CASE WHEN p.user_type = 'student' THEN 1 END) as students,
    COUNT(CASE WHEN p.user_type = 'tutor' THEN 1 END) as tutors
FROM sessions s
JOIN meeting_sessions ms ON s.id = ms.session_id
LEFT JOIN meeting_participants mp ON ms.id = mp.meeting_session_id
LEFT JOIN profiles p ON mp.user_id = p.id
WHERE s.id = 'demo-session-001'
GROUP BY s.id, s.scheduled_at;

-- =====================================================
-- 6. USER PREFERENCES TESTS
-- =====================================================

-- Check user meeting preferences
SELECT 
    ump.user_id,
    CONCAT(p.first_name, ' ', p.last_name) as user_name,
    p.user_type,
    mp.display_name as preferred_provider,
    ump.provider_settings,
    ump.auto_join_enabled,
    ump.recording_preference
FROM user_meeting_preferences ump
JOIN profiles p ON ump.user_id = p.id
JOIN meeting_providers mp ON ump.preferred_provider_id = mp.id
WHERE ump.user_id = 'demo-tutor-001';

-- =====================================================
-- 7. INTEGRATION STATUS OVERVIEW
-- =====================================================

-- Overall Teams integration status
SELECT 
    'Total Users' as metric,
    COUNT(*) as count
FROM profiles
WHERE user_type IN ('student', 'tutor')

UNION ALL

SELECT 
    'Users with Teams Integration' as metric,
    COUNT(DISTINCT ui.user_id) as count
FROM user_integrations ui
WHERE ui.provider = 'microsoft_teams'

UNION ALL

SELECT 
    'Active Teams Meetings' as metric,
    COUNT(*) as count
FROM sessions s
JOIN meeting_sessions ms ON s.id = ms.session_id
JOIN meeting_providers mp ON ms.provider_id = mp.id
WHERE mp.name = 'microsoft_teams'
AND s.status = 'scheduled'
AND s.scheduled_at > NOW()

UNION ALL

SELECT 
    'Completed Teams Meetings' as metric,
    COUNT(*) as count
FROM sessions s
JOIN meeting_sessions ms ON s.id = ms.session_id
JOIN meeting_providers mp ON ms.provider_id = mp.id
WHERE mp.name = 'microsoft_teams'
AND s.status = 'completed';

-- =====================================================
-- 8. ERROR CHECKING QUERIES
-- =====================================================

-- Check for sessions without meeting_sessions
SELECT 
    s.id,
    s.scheduled_at,
    s.meeting_url,
    'Missing meeting_session record' as issue
FROM sessions s
LEFT JOIN meeting_sessions ms ON s.id = ms.session_id
WHERE s.meeting_provider_id IS NOT NULL
AND ms.id IS NULL;

-- Check for expired tokens
SELECT 
    ui.user_id,
    CONCAT(p.first_name, ' ', p.last_name) as user_name,
    ui.provider,
    ui.expires_at,
    'Token expired' as issue
FROM user_integrations ui
JOIN profiles p ON ui.user_id = p.id
WHERE ui.expires_at < NOW();

-- Check for inactive providers being used
SELECT 
    s.id as session_id,
    mp.name as provider_name,
    mp.is_active,
    'Using inactive provider' as issue
FROM sessions s
JOIN meeting_providers mp ON s.meeting_provider_id = mp.id
WHERE mp.is_active = false;
