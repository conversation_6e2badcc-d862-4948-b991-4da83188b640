-- =====================================================
-- TEST ENHANCED TUTOR DATA FETCHING
-- =====================================================
-- Use these queries to test the enhanced tutor data fetching logic

-- =====================================================
-- 1. VERIFY TUTOR DATA EXISTS
-- =====================================================

-- Check if demo tutor exists in profiles table
SELECT 
    id,
    first_name,
    last_name,
    email,
    user_type,
    profile_picture_url,
    timezone
FROM profiles 
WHERE id = 'demo-tutor-001';

-- Check if demo tutor exists in tutors table
SELECT 
    id,
    education_level,
    hourly_rate,
    subjects_taught,
    teaching_experience,
    bio,
    verification_status,
    rating
FROM tutors 
WHERE id = 'demo-tutor-001';

-- Check if demo tutor exists in candidate_tutor table (fallback)
SELECT 
    id,
    education_level,
    hourly_rate,
    subjects_taught,
    teaching_experience,
    bio,
    verification_status,
    rating
FROM candidate_tutor 
WHERE id = 'demo-tutor-001';

-- =====================================================
-- 2. TEST COMPLETE TUTOR DATA QUERY
-- =====================================================

-- Simulate the complete tutor data fetch (profiles + tutors)
SELECT 
    p.id,
    p.first_name,
    p.last_name,
    p.email,
    p.profile_picture_url,
    p.timezone,
    t.education_level,
    t.hourly_rate,
    t.subjects_taught,
    t.teaching_experience,
    t.bio,
    t.verification_status,
    t.rating,
    -- Generate display name with credentials
    CASE 
        WHEN t.education_level ILIKE '%phd%' THEN CONCAT('Dr. ', p.first_name, ' ', p.last_name)
        WHEN t.education_level ILIKE '%master%' THEN CONCAT('Prof. ', p.first_name, ' ', p.last_name)
        ELSE CONCAT(p.first_name, ' ', p.last_name)
    END as display_name
FROM profiles p
LEFT JOIN tutors t ON p.id = t.id
WHERE p.id = 'demo-tutor-001';

-- =====================================================
-- 3. TEST SESSION WITH ENHANCED TUTOR DATA
-- =====================================================

-- Query sessions with complete tutor information
SELECT 
    s.id as session_id,
    s.scheduled_at,
    s.duration_min,
    s.status,
    
    -- Basic tutor profile
    p.first_name as tutor_first_name,
    p.last_name as tutor_last_name,
    p.email as tutor_email,
    p.profile_picture_url as tutor_photo,
    
    -- Enhanced tutor data
    t.education_level,
    t.hourly_rate,
    t.subjects_taught,
    t.bio,
    t.rating as tutor_rating,
    
    -- Generated display name
    CASE 
        WHEN t.education_level ILIKE '%phd%' THEN CONCAT('Dr. ', p.first_name, ' ', p.last_name)
        WHEN t.education_level ILIKE '%master%' THEN CONCAT('Prof. ', p.first_name, ' ', p.last_name)
        ELSE CONCAT(p.first_name, ' ', p.last_name)
    END as tutor_display_name,
    
    -- Topic information
    top.name as topic_name,
    sub.name as subtopic_name,
    
    -- Batch information
    b.name as batch_name
    
FROM sessions s
LEFT JOIN profiles p ON s.tutor_id = p.id
LEFT JOIN tutors t ON s.tutor_id = t.id
LEFT JOIN topics top ON s.topic_id = top.id
LEFT JOIN subtopics sub ON s.subtopic_id = sub.id
LEFT JOIN batches b ON s.batch_id = b.id
WHERE s.student_id = 'demo-student-001'
ORDER BY s.scheduled_at DESC;

-- =====================================================
-- 4. TEST FALLBACK TO CANDIDATE_TUTOR
-- =====================================================

-- Query with fallback to candidate_tutor table
SELECT 
    s.id as session_id,
    s.tutor_id,
    
    -- Basic profile
    p.first_name,
    p.last_name,
    p.email,
    
    -- Try tutors table first
    t.education_level as tutors_education,
    t.rating as tutors_rating,
    
    -- Fallback to candidate_tutor
    ct.education_level as candidate_education,
    ct.rating as candidate_rating,
    
    -- Use COALESCE to get the first non-null value
    COALESCE(t.education_level, ct.education_level) as final_education,
    COALESCE(t.rating, ct.rating) as final_rating,
    COALESCE(t.subjects_taught, ct.subjects_taught) as final_subjects
    
FROM sessions s
LEFT JOIN profiles p ON s.tutor_id = p.id
LEFT JOIN tutors t ON s.tutor_id = t.id
LEFT JOIN candidate_tutor ct ON s.tutor_id = ct.id
WHERE s.student_id = 'demo-student-001';

-- =====================================================
-- 5. VERIFY DATA COMPLETENESS
-- =====================================================

-- Check for sessions with missing tutor data
SELECT 
    s.id as session_id,
    s.tutor_id,
    CASE 
        WHEN p.id IS NULL THEN 'Missing profile'
        WHEN t.id IS NULL AND ct.id IS NULL THEN 'Missing tutor data'
        ELSE 'Complete data'
    END as data_status,
    p.first_name,
    p.last_name,
    COALESCE(t.education_level, ct.education_level) as education_level,
    COALESCE(t.rating, ct.rating) as rating
FROM sessions s
LEFT JOIN profiles p ON s.tutor_id = p.id
LEFT JOIN tutors t ON s.tutor_id = t.id
LEFT JOIN candidate_tutor ct ON s.tutor_id = ct.id
WHERE s.student_id = 'demo-student-001'
ORDER BY s.scheduled_at DESC;

-- =====================================================
-- 6. PERFORMANCE TEST
-- =====================================================

-- Test query performance for multiple sessions
EXPLAIN ANALYZE
SELECT 
    s.id,
    s.scheduled_at,
    p.first_name,
    p.last_name,
    COALESCE(t.education_level, ct.education_level) as education_level,
    COALESCE(t.rating, ct.rating) as rating
FROM sessions s
LEFT JOIN profiles p ON s.tutor_id = p.id
LEFT JOIN tutors t ON s.tutor_id = t.id
LEFT JOIN candidate_tutor ct ON s.tutor_id = ct.id
WHERE s.student_id = 'demo-student-001';

-- =====================================================
-- 7. EXPECTED RESULTS VERIFICATION
-- =====================================================

-- This query should return the enhanced tutor information
-- Expected: Dr. Sarah Johnson (if PhD) or Prof. Sarah Johnson (if Masters) or Sarah Johnson
SELECT 
    'Expected tutor display name test' as test_name,
    CASE 
        WHEN t.education_level ILIKE '%phd%' THEN CONCAT('Dr. ', p.first_name, ' ', p.last_name)
        WHEN t.education_level ILIKE '%master%' THEN CONCAT('Prof. ', p.first_name, ' ', p.last_name)
        ELSE CONCAT(p.first_name, ' ', p.last_name)
    END as display_name,
    t.education_level,
    t.rating,
    t.subjects_taught
FROM profiles p
LEFT JOIN tutors t ON p.id = t.id
WHERE p.id = 'demo-tutor-001';

-- =====================================================
-- 8. TROUBLESHOOTING QUERIES
-- =====================================================

-- If tutor name shows as "Unknown Tutor", check these:

-- 1. Check if tutor profile exists
SELECT 'Profile check' as check_type, COUNT(*) as count
FROM profiles 
WHERE id = 'demo-tutor-001';

-- 2. Check if session has correct tutor_id
SELECT 'Session tutor_id check' as check_type, tutor_id, COUNT(*) as count
FROM sessions 
WHERE student_id = 'demo-student-001'
GROUP BY tutor_id;

-- 3. Check for data type mismatches
SELECT 
    'Data type check' as check_type,
    pg_typeof(s.tutor_id) as session_tutor_id_type,
    pg_typeof(p.id) as profile_id_type
FROM sessions s
LEFT JOIN profiles p ON s.tutor_id = p.id
WHERE s.student_id = 'demo-student-001'
LIMIT 1;
