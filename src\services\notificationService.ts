import { supabase } from '@/lib/supabaseClient';
import { Notification } from '@/store/notificationStore';

export type NotificationType = 'billing' | 'academic' | 'system';

export interface CreateNotificationParams {
  userId: string;
  title: string;
  message: string;
  type: NotificationType;
}

export interface NotificationTemplate {
  title: string;
  message: string;
  type: NotificationType;
}

/**
 * Service for managing notifications
 */
export class NotificationService {
  /**
   * Create a new notification using database function
   */
  static async createNotification(params: CreateNotificationParams): Promise<Notification | null> {
    try {
      // Use database function for better validation and performance
      const { data, error } = await supabase.rpc('create_notification', {
        p_user_id: params.userId,
        p_title: params.title,
        p_message: params.message,
        p_type: params.type
      });

      if (error) {
        console.error('Error creating notification:', error);
        throw error;
      }

      // The function returns the notification ID, so we need to fetch the full notification
      if (data) {
        const { data: notification, error: fetchError } = await supabase
          .from('notifications')
          .select('*')
          .eq('id', data)
          .single();

        if (fetchError) {
          console.error('Error fetching created notification:', fetchError);
          return null;
        }

        return notification;
      }

      return null;
    } catch (error) {
      console.error('Failed to create notification:', error);
      return null;
    }
  }

  /**
   * Create multiple notifications for different users
   */
  static async createBulkNotifications(
    userIds: string[],
    template: NotificationTemplate
  ): Promise<Notification[]> {
    try {
      const notifications = userIds.map(userId => ({
        user_id: userId,
        title: template.title,
        message: template.message,
        type: template.type,
      }));

      const { data, error } = await supabase
        .from('notifications')
        .insert(notifications)
        .select();

      if (error) {
        console.error('Error creating bulk notifications:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Failed to create bulk notifications:', error);
      return [];
    }
  }

  /**
   * Get unread notification count for a user using database function
   */
  static async getUnreadCount(userId: string): Promise<number> {
    try {
      const { data, error } = await supabase.rpc('get_unread_notification_count', {
        p_user_id: userId
      });

      if (error) {
        console.error('Error getting unread count:', error);
        return 0;
      }

      return data || 0;
    } catch (error) {
      console.error('Failed to get unread count:', error);
      return 0;
    }
  }

  /**
   * Mark notification as read
   */
  static async markAsRead(notificationId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notificationId);

      if (error) {
        console.error('Error marking notification as read:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
      return false;
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  static async markAllAsRead(userId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('user_id', userId)
        .eq('is_read', false);

      if (error) {
        console.error('Error marking all notifications as read:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
      return false;
    }
  }

  /**
   * Delete a notification
   */
  static async deleteNotification(notificationId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', notificationId);

      if (error) {
        console.error('Error deleting notification:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Failed to delete notification:', error);
      return false;
    }
  }

  /**
   * Predefined notification templates
   */
  static templates = {
    // Billing notifications
    paymentSuccess: (productName: string): NotificationTemplate => ({
      title: 'Payment Successful',
      message: `Your payment for ${productName} has been processed successfully. Your subscription is now active.`,
      type: 'billing'
    }),

    paymentFailed: (productName: string): NotificationTemplate => ({
      title: 'Payment Failed',
      message: `We couldn't process your payment for ${productName}. Please update your payment method and try again.`,
      type: 'billing'
    }),

    subscriptionExpiring: (productName: string, daysLeft: number): NotificationTemplate => ({
      title: 'Subscription Expiring Soon',
      message: `Your ${productName} subscription will expire in ${daysLeft} days. Renew now to continue your learning journey.`,
      type: 'billing'
    }),

    subscriptionExpired: (productName: string): NotificationTemplate => ({
      title: 'Subscription Expired',
      message: `Your ${productName} subscription has expired. Renew now to regain access to your learning materials.`,
      type: 'billing'
    }),

    // Academic notifications
    sessionScheduled: (tutorName: string, date: string, time: string): NotificationTemplate => ({
      title: 'Session Scheduled',
      message: `Your session with ${tutorName} has been scheduled for ${date} at ${time}.`,
      type: 'academic'
    }),

    sessionCancelled: (tutorName: string, date: string): NotificationTemplate => ({
      title: 'Session Cancelled',
      message: `Your session with ${tutorName} scheduled for ${date} has been cancelled. Please reschedule at your convenience.`,
      type: 'academic'
    }),

    sessionReminder: (tutorName: string, timeUntil: string): NotificationTemplate => ({
      title: 'Session Reminder',
      message: `Your session with ${tutorName} starts in ${timeUntil}. Don't forget to join!`,
      type: 'academic'
    }),

    assignmentDue: (assignmentName: string, dueDate: string): NotificationTemplate => ({
      title: 'Assignment Due Soon',
      message: `Your assignment "${assignmentName}" is due on ${dueDate}. Make sure to submit it on time.`,
      type: 'academic'
    }),

    newMaterial: (materialName: string, subject: string): NotificationTemplate => ({
      title: 'New Learning Material Available',
      message: `New material "${materialName}" has been added to your ${subject} course. Check it out now!`,
      type: 'academic'
    }),

    // System notifications
    profileUpdated: (): NotificationTemplate => ({
      title: 'Profile Updated',
      message: 'Your profile has been successfully updated.',
      type: 'system'
    }),

    passwordChanged: (): NotificationTemplate => ({
      title: 'Password Changed',
      message: 'Your password has been successfully changed. If you didn\'t make this change, please contact support immediately.',
      type: 'system'
    }),

    accountVerified: (): NotificationTemplate => ({
      title: 'Account Verified',
      message: 'Your account has been successfully verified. Welcome to our platform!',
      type: 'system'
    }),

    maintenanceScheduled: (date: string, duration: string): NotificationTemplate => ({
      title: 'Scheduled Maintenance',
      message: `We have scheduled maintenance on ${date} for approximately ${duration}. The platform may be temporarily unavailable during this time.`,
      type: 'system'
    }),

    newFeature: (featureName: string): NotificationTemplate => ({
      title: 'New Feature Available',
      message: `We've added a new feature: ${featureName}. Check it out and let us know what you think!`,
      type: 'system'
    }),

    // Tutor-specific notifications
    sessionRequestReceived: (studentName: string, subject: string, date: string): NotificationTemplate => ({
      title: 'New Session Request',
      message: `${studentName} has requested a ${subject} session for ${date}. Please review and respond.`,
      type: 'academic'
    }),

    sessionRequestAccepted: (studentName: string, date: string, time: string): NotificationTemplate => ({
      title: 'Session Request Accepted',
      message: `Your session with ${studentName} for ${date} at ${time} has been confirmed.`,
      type: 'academic'
    }),

    sessionRequestRejected: (studentName: string, date: string, reason?: string): NotificationTemplate => ({
      title: 'Session Request Declined',
      message: `Your session request with ${studentName} for ${date} has been declined.${reason ? ` Reason: ${reason}` : ''}`,
      type: 'academic'
    }),

    studentJoinedBatch: (studentName: string, batchName: string): NotificationTemplate => ({
      title: 'New Student Joined',
      message: `${studentName} has joined your ${batchName} batch. Welcome them to get started!`,
      type: 'academic'
    }),

    paymentReceived: (amount: string, studentName: string, sessionDate: string): NotificationTemplate => ({
      title: 'Payment Received',
      message: `You've received ${amount} payment from ${studentName} for the session on ${sessionDate}.`,
      type: 'billing'
    }),

    profileApproved: (): NotificationTemplate => ({
      title: 'Profile Approved',
      message: 'Congratulations! Your tutor profile has been approved and is now live. Students can now book sessions with you.',
      type: 'system'
    }),

    profileRejected: (reason: string): NotificationTemplate => ({
      title: 'Profile Needs Updates',
      message: `Your tutor profile needs some updates before approval. Reason: ${reason}. Please make the necessary changes and resubmit.`,
      type: 'system'
    }),

    availabilityReminder: (): NotificationTemplate => ({
      title: 'Update Your Availability',
      message: 'Don\'t forget to update your availability for the upcoming week to help students book sessions with you.',
      type: 'system'
    }),

    sessionFeedbackReceived: (studentName: string, rating: number): NotificationTemplate => ({
      title: 'New Session Feedback',
      message: `${studentName} has left feedback for your recent session with a ${rating}-star rating. Check it out!`,
      type: 'academic'
    }),

    monthlyEarningsReport: (amount: string, sessionCount: number): NotificationTemplate => ({
      title: 'Monthly Earnings Report',
      message: `Your earnings for this month: ${amount} from ${sessionCount} sessions. Great work!`,
      type: 'billing'
    }),

    studentCancelledSession: (studentName: string, date: string, time: string): NotificationTemplate => ({
      title: 'Session Cancelled by Student',
      message: `${studentName} has cancelled the session scheduled for ${date} at ${time}. The slot is now available for other bookings.`,
      type: 'academic'
    }),

    resourceUploadApproved: (resourceName: string): NotificationTemplate => ({
      title: 'Resource Approved',
      message: `Your uploaded resource "${resourceName}" has been approved and is now available to your students.`,
      type: 'academic'
    }),

    resourceUploadRejected: (resourceName: string, reason: string): NotificationTemplate => ({
      title: 'Resource Needs Review',
      message: `Your uploaded resource "${resourceName}" needs some updates. Reason: ${reason}. Please make the necessary changes and resubmit.`,
      type: 'academic'
    }),

    // Tutor change request template
    tutorChangeRequest: (studentName: string, batchName: string, topicName: string, subtopicName: string | null, requestedTutorName: string, reason: string): NotificationTemplate => ({
      title: 'Tutor Change Request',
      message: `Student ${studentName} has requested a tutor change for:

Batch: ${batchName}
Topic: ${topicName}
${subtopicName ? `Subtopic: ${subtopicName}` : ''}
Requested Tutor: ${requestedTutorName}

Reason: ${reason}

Please review and approve/reject this request. The student will be notified once a decision is made.`,
      type: 'academic'
    }),

    // Student tutor change request confirmation
    tutorChangeRequestConfirmation: (): NotificationTemplate => ({
      title: 'Tutor Change Request',
      message: 'Your tutor change request has been received and is pending with admin. You will be notified once the request gets approved or rejected.',
      type: 'academic'
    })
  };

  /**
   * Helper method to create notifications using templates
   */
  static async createFromTemplate(
    userId: string,
    template: NotificationTemplate
  ): Promise<Notification | null> {
    return this.createNotification({
      userId,
      title: template.title,
      message: template.message,
      type: template.type
    });
  }

  /**
   * Helper method to create bulk notifications using templates
   */
  static async createBulkFromTemplate(
    userIds: string[],
    template: NotificationTemplate
  ): Promise<Notification[]> {
    return this.createBulkNotifications(userIds, template);
  }

  /**
   * Quick helper to create a notification with minimal parameters
   */
  static async quickNotify(
    userId: string,
    title: string,
    message: string,
    type: NotificationType = 'system'
  ): Promise<Notification | null> {
    return this.createNotification({
      userId,
      title,
      message,
      type
    });
  }
}
