import { supabase } from '@/lib/supabaseClient';

export interface SessionData {
  id: string;
  batch_id: string;
  topic_id: string;
  subtopic_id?: string;
  tutor_id: string;
  student_id: string;
  scheduled_at: string;
  duration_min: number;
  status: 'scheduled' | 'completed' | 'cancelled' | 'rescheduled';
  mode: 'video' | 'audio' | 'quiz' | 'hybrid';
  session_type: 'regular' | 'demo' | 'makeup' | 'assessment' | 'consultation';
  meeting_url?: string;
  location?: string;
  has_conflict?: boolean;
  urgency_level?: number;
  rescheduled_from?: string;
  created_by: 'admin' | 'tutor' | 'student';
  created_at: string;
  updated_at: string;
  meeting_provider_id?: string;
  
  // Joined data
  tutor_profile?: {
    first_name: string;
    last_name: string;
    profile_picture_url?: string;
    email: string;
    timezone?: string;
  };
  tutor_data?: {
    education_level?: string;
    hourly_rate?: number;
    subjects_taught?: string;
    teaching_experience?: string;
    bio?: string;
    verification_status?: string;
    rating?: number;
  };
  student_profile?: {
    first_name: string;
    last_name: string;
    profile_picture_url?: string;
    email: string;
  };
  topic?: {
    name: string;
    description?: string;
    icon?: string;
  };
  subtopic?: {
    name: string;
    description?: string;
  };
  batch?: {
    name: string;
    product_name: string;
  };
  meeting_session?: {
    meeting_url: string;
    join_url?: string;
    meeting_status: string;
    recording_enabled?: boolean;
    provider_meeting_id?: string;
  };
  meeting_provider?: {
    name: string;
    display_name: string;
  };
  session_feedback?: {
    rating?: number;
    comments?: string;
  }[];
}

export interface SessionStats {
  totalSessions: number;
  totalHours: number;
  completedSessions: number;
  upcomingSessions: number;
  averageRating: number;
  favoriteTopics: string[];
  favoriteTutors: string[];
}

class SessionsService {


  /**
   * Fetch sessions for a student with all related data
   */
  async fetchStudentSessions(studentId: string, status?: string): Promise<SessionData[]> {
    try {
      let query = supabase
        .from('sessions')
        .select(`
          *,
          tutor_profile:profiles!tutor_id(
            first_name,
            last_name,
            profile_picture_url,
            email,
            timezone
          ),
          student_profile:profiles!student_id(
            first_name,
            last_name,
            profile_picture_url,
            email
          ),
          topic:topics(
            name,
            description,
            icon
          ),
          subtopic:subtopics(
            name,
            description
          ),
          batch:batches(
            name,
            product_name
          ),
          meeting_session:meeting_sessions(
            meeting_url,
            join_url,
            meeting_status,
            recording_enabled,
            provider_meeting_id
          ),
          meeting_provider:meeting_providers(
            name,
            display_name
          ),
          session_feedback(
            rating,
            comments
          )
        `)
        .eq('student_id', studentId)
        .order('scheduled_at', { ascending: false });

      if (status) {
        query = query.eq('status', status);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching sessions:', error);
        throw new Error(`Failed to fetch sessions: ${error.message}`);
      }

      // Now fetch tutor data for each session
      const sessionsWithTutorData = await Promise.all(
        (data || []).map(async (session) => {
          let tutorData = null;

          if (session.tutor_id) {
            // Try to fetch from tutors table first
            const { data: tutorTableData } = await supabase
              .from('tutors')
              .select(`
                education_level,
                hourly_rate,
                subjects_taught,
                teaching_experience,
                bio,
                verification_status,
                rating
              `)
              .eq('id', session.tutor_id)
              .single();

            // If not found in tutors table, try candidate_tutor table
            if (!tutorTableData) {
              const { data: candidateData } = await supabase
                .from('candidate_tutor')
                .select(`
                  education_level,
                  hourly_rate,
                  subjects_taught,
                  teaching_experience,
                  bio,
                  verification_status,
                  rating
                `)
                .eq('id', session.tutor_id)
                .single();

              tutorData = candidateData;
            } else {
              tutorData = tutorTableData;
            }
          }

          return {
            ...session,
            tutor_data: tutorData
          };
        })
      );

      return sessionsWithTutorData;
    } catch (error) {
      console.error('Error in fetchStudentSessions:', error);
      throw error;
    }
  }

  /**
   * Fetch upcoming sessions for a student
   */
  async fetchUpcomingSessions(studentId: string): Promise<SessionData[]> {
    try {
      const { data, error } = await supabase
        .from('sessions')
        .select(`
          *,
          tutor_profile:profiles!tutor_id(
            first_name,
            last_name,
            profile_picture_url,
            email,
            timezone
          ),
          topic:topics(
            name,
            description,
            icon
          ),
          subtopic:subtopics(
            name,
            description
          ),
          batch:batches(
            name,
            product_name
          ),
          meeting_session:meeting_sessions(
            meeting_url,
            join_url,
            meeting_status,
            recording_enabled,
            provider_meeting_id
          ),
          meeting_provider:meeting_providers(
            name,
            display_name
          )
        `)
        .eq('student_id', studentId)
        .eq('status', 'scheduled')
        .gte('scheduled_at', new Date().toISOString())
        .order('scheduled_at', { ascending: true });

      if (error) {
        console.error('Error fetching upcoming sessions:', error);
        throw new Error(`Failed to fetch upcoming sessions: ${error.message}`);
      }

      // Now fetch tutor profile and tutor data for each session
      const sessionsWithTutorData = await Promise.all(
        (data || []).map(async (session) => {
          let tutorProfile = null;
          let tutorData = null;

          if (session.tutor_id) {


            // Fetch tutor profile
            // Note: This may fail due to RLS policies. To fix, add comprehensive RLS policy to profiles table
            // that allows students and tutors to view each other's profiles at all assignment levels:
            // - Session level (sessions.tutor_id)
            // - Batch level (batches.default_tutor_id)
            // - Topic level (batch_topics.custom_tutor_id)
            // - Subtopic level (batch_subtopics.custom_tutor_id)
            // See full RLS policy in documentation.
            const { data: profileData, error: profileError } = await supabase
              .from('profiles')
              .select(`
                first_name,
                last_name,
                profile_picture_url,
                email,
                timezone
              `)
              .eq('id', session.tutor_id)
              .single();

            if (profileError) {
              if (profileError.code === '42P17') {
                console.error('RLS policy causing infinite recursion. Please fix the RLS policy on profiles table.');
              } else {
                console.warn(`RLS policy blocking access to tutor profile ${session.tutor_id}.`);
              }

              // Since RLS is blocking profile access, we'll create a fallback profile
              tutorProfile = {
                first_name: 'Tutor',
                last_name: '',
                profile_picture_url: null,
                email: null,
                timezone: null
              };
            } else {
              tutorProfile = profileData;
            }

            // Try to fetch from tutors table first
            const { data: tutorTableData } = await supabase
              .from('tutors')
              .select(`
                education_level,
                hourly_rate,
                subjects_taught,
                teaching_experience,
                bio,
                verification_status,
                rating
              `)
              .eq('id', session.tutor_id)
              .single();

            // If not found in tutors table, try candidate_tutor table
            if (!tutorTableData) {
              const { data: candidateData } = await supabase
                .from('candidate_tutor')
                .select(`
                  education_level,
                  hourly_rate,
                  subjects_taught,
                  teaching_experience,
                  bio,
                  verification_status,
                  rating
                `)
                .eq('id', session.tutor_id)
                .single();

              tutorData = candidateData;
            } else {
              tutorData = tutorTableData;
            }
          }

          // If we have tutor data but no profile (due to RLS), try to enhance the fallback profile
          if (tutorProfile && tutorProfile.first_name === 'Tutor' && tutorData) {
            // Check if tutor data has any name information we can use
            if (tutorData.bio && tutorData.bio.includes('I am ')) {
              // Try to extract name from bio if it follows a pattern like "I am John Doe"
              const nameMatch = tutorData.bio.match(/I am ([A-Za-z\s]+)/);
              if (nameMatch) {
                const fullName = nameMatch[1].trim();
                const nameParts = fullName.split(' ');
                tutorProfile.first_name = nameParts[0] || 'Tutor';
                tutorProfile.last_name = nameParts.slice(1).join(' ') || '';
              }
            }
          }

          return {
            ...session,
            tutor_profile: tutorProfile,
            tutor_data: tutorData
          };
        })
      );

      return sessionsWithTutorData;
    } catch (error) {
      console.error('Error in fetchUpcomingSessions:', error);
      throw error;
    }
  }

  /**
   * Fetch past sessions for a student
   */
  async fetchPastSessions(studentId: string): Promise<SessionData[]> {
    try {
      const { data, error } = await supabase
        .from('sessions')
        .select(`
          *,
          tutor_profile:profiles!tutor_id(
            first_name,
            last_name,
            profile_picture_url,
            email,
            timezone
          ),
          topic:topics(
            name,
            description,
            icon
          ),
          subtopic:subtopics(
            name,
            description
          ),
          batch:batches(
            name,
            product_name
          ),
          session_feedback(
            rating,
            comments
          )
        `)
        .eq('student_id', studentId)
        .eq('status', 'completed')
        .order('scheduled_at', { ascending: false });

      if (error) {
        console.error('Error fetching past sessions:', error);
        throw new Error(`Failed to fetch past sessions: ${error.message}`);
      }

      // Now fetch tutor data for each session
      const sessionsWithTutorData = await Promise.all(
        (data || []).map(async (session) => {
          let tutorData = null;

          if (session.tutor_id) {
            // Try to fetch from tutors table first
            const { data: tutorTableData } = await supabase
              .from('tutors')
              .select(`
                education_level,
                hourly_rate,
                subjects_taught,
                teaching_experience,
                bio,
                verification_status,
                rating
              `)
              .eq('id', session.tutor_id)
              .single();

            // If not found in tutors table, try candidate_tutor table
            if (!tutorTableData) {
              const { data: candidateData } = await supabase
                .from('candidate_tutor')
                .select(`
                  education_level,
                  hourly_rate,
                  subjects_taught,
                  teaching_experience,
                  bio,
                  verification_status,
                  rating
                `)
                .eq('id', session.tutor_id)
                .single();

              tutorData = candidateData;
            } else {
              tutorData = tutorTableData;
            }
          }

          return {
            ...session,
            tutor_data: tutorData
          };
        })
      );

      return sessionsWithTutorData;
    } catch (error) {
      console.error('Error in fetchPastSessions:', error);
      throw error;
    }
  }

  /**
   * Calculate session statistics for a student
   */
  async calculateSessionStats(studentId: string): Promise<SessionStats> {
    try {
      const allSessions = await this.fetchStudentSessions(studentId);
      
      const totalSessions = allSessions.length;
      const completedSessions = allSessions.filter(s => s.status === 'completed').length;
      const upcomingSessions = allSessions.filter(s => s.status === 'scheduled' && new Date(s.scheduled_at) > new Date()).length;
      
      // Calculate total hours
      const totalHours = allSessions.reduce((total, session) => {
        return total + (session.duration_min / 60);
      }, 0);

      // Calculate average rating
      const ratingsData = allSessions
        .filter(s => s.session_feedback && s.session_feedback.length > 0)
        .flatMap(s => s.session_feedback?.filter(f => f.rating) || []);
      
      const averageRating = ratingsData.length > 0 
        ? ratingsData.reduce((sum, feedback) => sum + (feedback.rating || 0), 0) / ratingsData.length
        : 0;

      // Get favorite topics (most frequent)
      const topicCounts = allSessions.reduce((acc, session) => {
        const topicName = session.topic?.name || 'Unknown Topic';
        acc[topicName] = (acc[topicName] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const favoriteTopics = Object.entries(topicCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 3)
        .map(([topic]) => topic);

      // Get favorite tutors (most frequent)
      const tutorCounts = allSessions.reduce((acc, session) => {
        const tutorName = session.tutor_profile 
          ? `${session.tutor_profile.first_name} ${session.tutor_profile.last_name}`
          : 'Unknown Tutor';
        acc[tutorName] = (acc[tutorName] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const favoriteTutors = Object.entries(tutorCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 3)
        .map(([tutor]) => tutor);

      return {
        totalSessions,
        totalHours: Math.round(totalHours * 10) / 10, // Round to 1 decimal place
        completedSessions,
        upcomingSessions,
        averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
        favoriteTopics,
        favoriteTutors
      };
    } catch (error) {
      console.error('Error calculating session stats:', error);
      throw error;
    }
  }

  /**
   * Get session by ID with full details
   */
  async getSessionById(sessionId: string): Promise<SessionData | null> {
    try {
      const { data, error } = await supabase
        .from('sessions')
        .select(`
          *,
          tutor_profile:profiles!tutor_id(
            first_name,
            last_name,
            profile_picture_url,
            email,
            timezone
          ),
          student_profile:profiles!student_id(
            first_name,
            last_name,
            profile_picture_url,
            email
          ),
          topic:topics(
            name,
            description,
            icon
          ),
          subtopic:subtopics(
            name,
            description
          ),
          batch:batches(
            name,
            product_name
          ),
          meeting_session:meeting_sessions(
            meeting_url,
            join_url,
            meeting_status,
            recording_enabled,
            provider_meeting_id
          ),
          meeting_provider:meeting_providers(
            name,
            display_name
          ),
          session_feedback(
            rating,
            comments
          )
        `)
        .eq('id', sessionId)
        .single();

      if (error) {
        console.error('Error fetching session by ID:', error);
        return null;
      }

      // Fetch tutor data
      let tutorData = null;
      if (data.tutor_id) {
        // Try to fetch from tutors table first
        const { data: tutorTableData } = await supabase
          .from('tutors')
          .select(`
            education_level,
            hourly_rate,
            subjects_taught,
            teaching_experience,
            bio,
            verification_status,
            rating
          `)
          .eq('id', data.tutor_id)
          .single();

        // If not found in tutors table, try candidate_tutor table
        if (!tutorTableData) {
          const { data: candidateData } = await supabase
            .from('candidate_tutor')
            .select(`
              education_level,
              hourly_rate,
              subjects_taught,
              teaching_experience,
              bio,
              verification_status,
              rating
            `)
            .eq('id', data.tutor_id)
            .single();

          tutorData = candidateData;
        } else {
          tutorData = tutorTableData;
        }
      }

      return {
        ...data,
        tutor_data: tutorData
      };
    } catch (error) {
      console.error('Error in getSessionById:', error);
      return null;
    }
  }
}

export const sessionsService = new SessionsService();
