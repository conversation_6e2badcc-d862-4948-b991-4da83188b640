import React, { useState } from "react";
import { useSessions } from "@/hooks/useSessions";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/Table";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
} from "@/components/ui/DropdownMenu";
import { Badge } from "@/components/ui/Badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import {
  Search,
  Filter,
  Columns,
  Calendar,
  MoreHorizontal,
  Video,
  RefreshCw,
  Clock,
  Calendar as CalendarIcon,
  Edit2,
  Star,
  X,
  AlertCircle,
  Grid,
  List,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/Dialog";
import { useProfileData } from "@/hooks/useProfileData";
import StudentPageLayout from "@/components/layouts/StudentPageLayout";
import LoadingSpinner from "@/components/LoadingSpinner";
import { Link } from "react-router-dom";
import SessionCard, { formatSessionTime, getTutorDisplayName, getTopicName, getTutorPhoto, getTutorRating, generateSessionId, getSubjectName } from "@/components/student/SessionCard";





const StudentUpcomingSessions = () => {
  // Use real sessions hook instead of mock store
  const {
    upcomingSessions,
    isLoading: isLoadingSessions,
    error: sessionsError,
    refreshSessions
  } = useSessions();

  // Local state for UI
  const [searchTerm, setSearchTerm] = useState("");
  const [isCardView, setIsCardView] = useState(false);
  const [selectedSession, setSelectedSession] = useState<any>(null);
  const [showCancelDialog, setShowCancelDialog] = useState(false);

  // Column visibility state
  const [visibleColumns, setVisibleColumns] = useState({
    sessionId: true,
    tutor: true,
    subject: true,
    topic: true,
    subtopic: false,
    sessionType: true,
    dateTime: true,
    duration: true,
    status: true,
    actions: true,
  });

  // Filter state
  const [filters, setFilters] = useState({
    status: [] as string[],
    sessionType: [] as string[],
    subject: [] as string[],
  });

  // Get profile data for the UserNavbar
  const profileData = useProfileData();



  // Get unique filter options
  const getFilterOptions = () => {
    const statuses = [...new Set(upcomingSessions.map(s => s.status))];
    const sessionTypes = [...new Set(upcomingSessions.map(s => s.session_type))];
    const subjects = [...new Set(upcomingSessions.map(s => getSubjectName(s.topic)).filter(Boolean))];

    return { statuses, sessionTypes, subjects };
  };

  const { statuses, sessionTypes, subjects } = getFilterOptions();

  // Filter sessions based on search term and filters
  const filteredSessions = upcomingSessions.filter(session => {
    // Search term filter
    if (searchTerm) {
      const tutorName = getTutorDisplayName(session.tutor_profile, session.tutor_data).toLowerCase();
      const topicName = getTopicName(session.topic, session.subtopic).toLowerCase();
      const batchName = session.batch?.name?.toLowerCase() || '';
      const sessionId = generateSessionId(session.id).toLowerCase();

      const matchesSearch = tutorName.includes(searchTerm.toLowerCase()) ||
                           topicName.includes(searchTerm.toLowerCase()) ||
                           batchName.includes(searchTerm.toLowerCase()) ||
                           sessionId.includes(searchTerm.toLowerCase());

      if (!matchesSearch) return false;
    }

    // Status filter
    if (filters.status.length > 0 && !filters.status.includes(session.status)) {
      return false;
    }

    // Session type filter
    if (filters.sessionType.length > 0 && !filters.sessionType.includes(session.session_type)) {
      return false;
    }

    // Subject filter
    if (filters.subject.length > 0) {
      const sessionSubject = getSubjectName(session.topic);
      if (!filters.subject.includes(sessionSubject)) {
        return false;
      }
    }

    return true;
  });

  const handleJoinSession = (session: any) => {
    // Navigate to join session page
    window.open(`/join-session/${session.id}`, '_blank');
  };

  const handleRescheduleSession = (session: any) => {
    // Navigate to reschedule page
    window.location.href = `/edit-session/${session.id}`;
  };

  const handleCancelSession = (session: any) => {
    setSelectedSession(session);
    setShowCancelDialog(true);
  };

  const confirmCancelSession = () => {
    if (selectedSession) {
      console.log(`Cancelling session ${selectedSession.id}`);
      // TODO: Implement actual cancellation logic
      setShowCancelDialog(false);
      setSelectedSession(null);
    }
  };

  const handleActionsClick = (session: any, action: string) => {
    if (action === "Join") {
      handleJoinSession(session);
    } else if (action === "Reschedule") {
      handleRescheduleSession(session);
    } else if (action === "Cancel") {
      handleCancelSession(session);
    }
  };

  // Filter toggle functions
  const toggleFilter = (filterType: keyof typeof filters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: prev[filterType].includes(value)
        ? prev[filterType].filter(item => item !== value)
        : [...prev[filterType], value]
    }));
  };

  const clearAllFilters = () => {
    setFilters({
      status: [],
      sessionType: [],
      subject: [],
    });
  };

  const hasActiveFilters = filters.status.length > 0 || filters.sessionType.length > 0 || filters.subject.length > 0;

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      "scheduled": { color: "bg-green-100 text-green-800", icon: <Clock size={12} /> },
      "pending": { color: "bg-yellow-100 text-yellow-800", icon: <Clock size={12} /> },
      "cancelled": { color: "bg-red-100 text-red-800", icon: null },
      "completed": { color: "bg-blue-100 text-blue-800", icon: null },
      "rescheduled": { color: "bg-orange-100 text-orange-800", icon: null },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      color: "bg-gray-100 text-gray-800",
      icon: null
    };

    return (
      <Badge className={`${config.color} flex items-center gap-1 capitalize`}>
        {config.icon}
        {status}
      </Badge>
    );
  };



  const getActionButtons = (session: any) => {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => handleActionsClick(session, "Join")}>
            <Video className="mr-2 h-4 w-4" />
            Join
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleActionsClick(session, "Reschedule")}>
            <Edit2 className="mr-2 h-4 w-4" />
            Reschedule
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => handleActionsClick(session, "Cancel")}
            className="text-red-600 focus:text-red-600"
          >
            <X className="mr-2 h-4 w-4" />
            Cancel
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  };

  return (
    <StudentPageLayout
      title="Upcoming Sessions"
      description="View and manage your scheduled learning sessions"
    >
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="p-4 border-b border-gray-200 flex flex-wrap justify-between items-center gap-4">
          <div className="relative flex-grow max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              type="text"
              placeholder="Search by tutor, subject, or topic"
              className="pl-10 pr-4 py-2 w-full border-gray-200 rounded-md focus:border-rfpurple-500 focus:ring focus:ring-rfpurple-200 focus:ring-opacity-50 transition-colors"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="flex items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <Filter size={16} />
                  Filter
                  {hasActiveFilters && (
                    <Badge variant="secondary" className="ml-1 h-4 w-4 p-0 text-xs">
                      {filters.status.length + filters.sessionType.length + filters.subject.length}
                    </Badge>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-64">
                <div className="p-2 text-sm font-medium text-gray-700 border-b">
                  Filter Options
                </div>

                {/* Status Filter */}
                <div className="p-2">
                  <div className="text-xs font-medium text-gray-600 mb-2">Status</div>
                  {statuses.map(status => (
                    <DropdownMenuCheckboxItem
                      key={status}
                      checked={filters.status.includes(status)}
                      onCheckedChange={() => toggleFilter('status', status)}
                      className="capitalize"
                    >
                      {status}
                    </DropdownMenuCheckboxItem>
                  ))}
                </div>

                {/* Session Type Filter */}
                <div className="p-2 border-t">
                  <div className="text-xs font-medium text-gray-600 mb-2">Session Type</div>
                  {sessionTypes.map(type => (
                    <DropdownMenuCheckboxItem
                      key={type}
                      checked={filters.sessionType.includes(type)}
                      onCheckedChange={() => toggleFilter('sessionType', type)}
                      className="capitalize"
                    >
                      {type}
                    </DropdownMenuCheckboxItem>
                  ))}
                </div>

                {/* Subject Filter */}
                <div className="p-2 border-t">
                  <div className="text-xs font-medium text-gray-600 mb-2">Subject</div>
                  {subjects.map(subject => (
                    <DropdownMenuCheckboxItem
                      key={subject}
                      checked={filters.subject.includes(subject)}
                      onCheckedChange={() => toggleFilter('subject', subject)}
                    >
                      {subject}
                    </DropdownMenuCheckboxItem>
                  ))}
                </div>

                {hasActiveFilters && (
                  <div className="p-2 border-t">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={clearAllFilters}
                      className="w-full"
                    >
                      Clear All Filters
                    </Button>
                  </div>
                )}
              </DropdownMenuContent>
            </DropdownMenu>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <Columns size={16} />
                  Columns
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <div className="p-2 text-sm font-medium text-gray-700 border-b">
                  Toggle Columns
                </div>
                {Object.entries(visibleColumns).map(([key, value]) => (
                  <DropdownMenuCheckboxItem
                    key={key}
                    checked={value}
                    onCheckedChange={(checked) =>
                      setVisibleColumns(prev => ({ ...prev, [key]: checked }))
                    }
                    className="capitalize"
                  >
                    {key === "sessionId" ? "Session ID" :
                     key === "dateTime" ? "Date & Time" :
                     key === "sessionType" ? "Session Type" : key}
                  </DropdownMenuCheckboxItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsCardView(!isCardView)}
              className="flex items-center gap-2"
            >
              {isCardView ? <List size={16} /> : <Grid size={16} />}
              {isCardView ? "Table View" : "Card View"}
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={refreshSessions}
              className="flex items-center gap-2"
              disabled={isLoadingSessions}
            >
              <RefreshCw size={16} className={isLoadingSessions ? "animate-spin" : ""} />
              Refresh
            </Button>
          </div>
        </div>

        {/* Content Area */}
        {isLoadingSessions ? (
          <div className="flex items-center justify-center py-8">
            <LoadingSpinner />
            <span className="ml-2">Loading sessions...</span>
          </div>
        ) : sessionsError ? (
          <div className="text-center py-6">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-4">{sessionsError}</p>
            <Button onClick={refreshSessions} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        ) : filteredSessions.length === 0 ? (
          <div className="text-center py-8">
            <Calendar className="h-12 w-12 mx-auto text-gray-300 mb-2" />
            <p className="text-gray-500 mb-4">
              {upcomingSessions.length === 0
                ? "You don't have any upcoming sessions."
                : "No sessions match your search criteria."}
            </p>
            {upcomingSessions.length === 0 && (
              <Button asChild>
                <Link to="/tutor-search">Find a Tutor</Link>
              </Button>
            )}
          </div>
        ) : isCardView ? (
          // Card View (reused from Dashboard)
          <div className="p-6 space-y-6">
            {filteredSessions.map((session) => (
              <SessionCard
                key={session.id}
                session={session}
                showSessionId={visibleColumns.sessionId}
                onReschedule={handleRescheduleSession}
                onJoin={handleJoinSession}
                onCancel={handleCancelSession}
              />
            ))}
          </div>
        ) : (
          // Table View
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  {visibleColumns.sessionId && <TableHead>Session ID</TableHead>}
                  {visibleColumns.tutor && <TableHead>Tutor</TableHead>}
                  {visibleColumns.subject && <TableHead>Subject</TableHead>}
                  {visibleColumns.topic && <TableHead>Topic</TableHead>}
                  {visibleColumns.subtopic && <TableHead>Subtopic</TableHead>}
                  {visibleColumns.sessionType && <TableHead>Session Type</TableHead>}
                  {visibleColumns.dateTime && <TableHead>Date & Time</TableHead>}
                  {visibleColumns.duration && <TableHead>Duration</TableHead>}
                  {visibleColumns.status && <TableHead>Status</TableHead>}
                  {visibleColumns.actions && <TableHead>Actions</TableHead>}
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSessions.map((session) => {
                  const { date, time } = formatSessionTime(session.scheduled_at);
                  const tutorDisplayName = getTutorDisplayName(session.tutor_profile, session.tutor_data);
                  const tutorPhoto = getTutorPhoto(session.tutor_profile);
                  const topicName = getTopicName(session.topic, session.subtopic);
                  const tutorRating = getTutorRating(session.tutor_data);
                  const sessionId = generateSessionId(session.id);

                  return (
                    <TableRow key={session.id}>
                      {visibleColumns.sessionId && (
                        <TableCell className="font-medium">{sessionId}</TableCell>
                      )}
                      {visibleColumns.tutor && (
                        <TableCell>
                          <div className="flex items-center">
                            <img
                              src={tutorPhoto}
                              alt={tutorDisplayName}
                              className="w-8 h-8 rounded-full object-cover mr-2"
                            />
                            <div>
                              <div className="flex items-center gap-1">
                                <span className="font-medium">{tutorDisplayName}</span>
                                {tutorRating && (
                                  <div className="flex items-center">
                                    <Star className="h-3 w-3 text-yellow-400 fill-yellow-400" />
                                    <span className="text-xs text-gray-600">{tutorRating}</span>
                                  </div>
                                )}
                              </div>
                              {session.tutor_data?.education_level && (
                                <p className="text-xs text-gray-500">{session.tutor_data.education_level}</p>
                              )}
                            </div>
                          </div>
                        </TableCell>
                      )}
                      {visibleColumns.subject && (
                        <TableCell>
                          <Badge variant="outline">{getSubjectName(session.topic)}</Badge>
                        </TableCell>
                      )}
                      {visibleColumns.topic && (
                        <TableCell>
                          <div>
                            <div className="font-medium">{topicName}</div>
                            {session.batch?.name && (
                              <p className="text-xs text-gray-500 mt-1">
                                {session.batch.name}
                              </p>
                            )}
                          </div>
                        </TableCell>
                      )}
                      {visibleColumns.subtopic && (
                        <TableCell>
                          {session.subtopic?.name || '—'}
                        </TableCell>
                      )}
                      {visibleColumns.sessionType && (
                        <TableCell>
                          <Badge variant="secondary" className="capitalize">
                            {session.session_type}
                          </Badge>
                        </TableCell>
                      )}
                      {visibleColumns.dateTime && (
                        <TableCell>
                          <div>
                            <div className="font-medium">{date}</div>
                            <div className="text-sm text-gray-500">{time}</div>
                          </div>
                        </TableCell>
                      )}
                      {visibleColumns.duration && (
                        <TableCell>{session.duration_min} min</TableCell>
                      )}
                      {visibleColumns.status && (
                        <TableCell>{getStatusBadge(session.status)}</TableCell>
                      )}
                      {visibleColumns.actions && (
                        <TableCell>{getActionButtons(session)}</TableCell>
                      )}
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        )}
      </div>

      {/* Cancel Session Dialog */}
      <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cancel Session</DialogTitle>
            <DialogDescription>
              {selectedSession && (
                <>
                  Are you sure you want to cancel your session with{' '}
                  {getTutorDisplayName(selectedSession.tutor_profile, selectedSession.tutor_data)}{' '}
                  on {formatSessionTime(selectedSession.scheduled_at).date} at{' '}
                  {formatSessionTime(selectedSession.scheduled_at).time}?
                  <br /><br />
                  This action cannot be undone. You may be charged a cancellation fee depending on the timing.
                </>
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCancelDialog(false)}>
              Keep Session
            </Button>
            <Button
              onClick={confirmCancelSession}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Cancel Session
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </StudentPageLayout>
  );
};

export default StudentUpcomingSessions;
