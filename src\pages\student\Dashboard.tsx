import { useEffect } from "react";
import { useAuth } from "@/context/AuthContext";
import { useNavigate } from "react-router-dom";
import { useProfileData } from "@/hooks/useProfileData";
import { useSessions } from "@/hooks/useSessions";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/Tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import LoadingSpinner from "@/components/LoadingSpinner";
import StudentPageLayout from "@/components/layouts/StudentPageLayout";
import NotificationDropdown from "@/components/notifications/NotificationDropdown";
import { create } from "zustand";
import { Link } from "react-router-dom";
import StudentCalendar, { CalendarEvent } from "@/components/student/StudentCalendar";
import UpcomingEvents from "@/components/student/UpcomingEvents";
import BillingSummary from "@/components/student/dashboard/BillingSummary";
import { useBillingStore } from "@/store/billingStore";
import SessionCard, { formatSessionTime, getTutorDisplayName, getTopicName, getTutorPhoto, getTutorRating, generateSessionId, getSubjectName } from "@/components/student/SessionCard";
import {
  Clock,
  User,
  BarChart,
  BookOpen,
  Star,
  Laptop,
  BookText,
  Beaker,
  Brain,
  Pencil,
  AlertCircle,
  RefreshCw,
} from "lucide-react";



import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
  TableHead,
} from "@/components/ui/Table";

// Create a comprehensive Zustand store for student dashboard
interface StudentDashboardState {
  // State
  activeTab: string;
  showWelcomeMessage: boolean;
  selectedDate: Date;
  tutorAvailability: string;
  currentMonth: Date;
  calendarView: "day" | "week" | "month";

  // Actions
  setActiveTab: (tab: string) => void;
  setShowWelcomeMessage: (show: boolean) => void;
  setSelectedDate: (date: Date) => void;
  setTutorAvailability: (availability: string) => void;
  setCurrentMonth: (date: Date) => void;
  setCalendarView: (view: "day" | "week" | "month") => void;
  nextMonth: () => void;
  prevMonth: () => void;
}

export const useStudentDashboardStore = create<StudentDashboardState>(
  (set, get) => ({
    // Initial state
    activeTab: "sessions", // Set sessions as default tab
    showWelcomeMessage: false,
    selectedDate: new Date(),
    tutorAvailability: "all",
    currentMonth: new Date(),
    calendarView: "week", // Set week as default view

    // Actions
    setActiveTab: (tab) => set({ activeTab: tab }),
    setShowWelcomeMessage: (show) => set({ showWelcomeMessage: show }),
    setSelectedDate: (date) => set({ selectedDate: date }),
    setTutorAvailability: (availability) =>
      set({ tutorAvailability: availability }),
    setCurrentMonth: (date) => set({ currentMonth: date }),
    setCalendarView: (view) => set({ calendarView: view }),
    nextMonth: () => {
      const calendarView = get().calendarView;

      if (calendarView === "day") {
        // For day view, move to the next day
        const selectedDate = get().selectedDate;
        const nextDay = new Date(selectedDate);
        nextDay.setDate(selectedDate.getDate() + 1);

        // Update both selected date and current month if month changes
        set({ selectedDate: nextDay });
        if (nextDay.getMonth() !== selectedDate.getMonth()) {
          const newMonth = new Date(nextDay);
          set({ currentMonth: newMonth });
        }
      }
      else if (calendarView === "week") {
        // For week view, move to the next week (7 days)
        const selectedDate = get().selectedDate;
        const nextWeek = new Date(selectedDate);
        nextWeek.setDate(selectedDate.getDate() + 7);

        // Update both selected date and current month if month changes
        set({ selectedDate: nextWeek });
        if (nextWeek.getMonth() !== selectedDate.getMonth()) {
          const newMonth = new Date(nextWeek);
          set({ currentMonth: newMonth });
        }
      }
      else {
        // For month view, move to the next month
        const current = get().currentMonth;
        const nextMonth = new Date(current);
        nextMonth.setMonth(current.getMonth() + 1);
        set({ currentMonth: nextMonth });

        // Also update selected date to be in the new month
        const selectedDate = get().selectedDate;
        const newSelectedDate = new Date(selectedDate);
        newSelectedDate.setMonth(selectedDate.getMonth() + 1);

        // Adjust for month length differences
        const daysInNewMonth = new Date(newSelectedDate.getFullYear(), newSelectedDate.getMonth() + 1, 0).getDate();
        if (newSelectedDate.getDate() > daysInNewMonth) {
          newSelectedDate.setDate(daysInNewMonth);
        }

        set({ selectedDate: newSelectedDate });
      }
    },
    prevMonth: () => {
      const calendarView = get().calendarView;

      if (calendarView === "day") {
        // For day view, move to the previous day
        const selectedDate = get().selectedDate;
        const prevDay = new Date(selectedDate);
        prevDay.setDate(selectedDate.getDate() - 1);

        // Update both selected date and current month if month changes
        set({ selectedDate: prevDay });
        if (prevDay.getMonth() !== selectedDate.getMonth()) {
          const newMonth = new Date(prevDay);
          set({ currentMonth: newMonth });
        }
      }
      else if (calendarView === "week") {
        // For week view, move to the previous week (7 days)
        const selectedDate = get().selectedDate;
        const prevWeek = new Date(selectedDate);
        prevWeek.setDate(selectedDate.getDate() - 7);

        // Update both selected date and current month if month changes
        set({ selectedDate: prevWeek });
        if (prevWeek.getMonth() !== selectedDate.getMonth()) {
          const newMonth = new Date(prevWeek);
          set({ currentMonth: newMonth });
        }
      }
      else {
        // For month view, move to the previous month
        const current = get().currentMonth;
        const prevMonth = new Date(current);
        prevMonth.setMonth(current.getMonth() - 1);
        set({ currentMonth: prevMonth });

        // Also update selected date to be in the new month
        const selectedDate = get().selectedDate;
        const newSelectedDate = new Date(selectedDate);
        newSelectedDate.setMonth(selectedDate.getMonth() - 1);

        // Adjust for month length differences
        const daysInNewMonth = new Date(newSelectedDate.getFullYear(), newSelectedDate.getMonth() + 1, 0).getDate();
        if (newSelectedDate.getDate() > daysInNewMonth) {
          newSelectedDate.setDate(daysInNewMonth);
        }

        set({ selectedDate: newSelectedDate });
      }
    },
  })
);

// Helper function to get session rating
const getSessionRating = (sessionFeedback: any[]) => {
  if (!sessionFeedback || sessionFeedback.length === 0) return null;
  const studentFeedback = sessionFeedback.find(f => f.rating);
  return studentFeedback?.rating || null;
};

// Sample calendar events data
const calendarEvents: CalendarEvent[] = [
  {
    id: 1,
    title: "Reinforcement Learning",
    tutorName: "Dr. Sarah Johnson",
    tutorPhoto: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
    date: new Date(2025, 3, 15), // April 15, 2025
    startTime: "10:00 AM",
    endTime: "11:00 AM",
    location: "Online",
    type: "session",
    color: "bg-blue-100 border-blue-300 text-blue-800",
    icon: <Laptop className="h-4 w-4 text-blue-500" />
  },
  {
    id: 2,
    title: "NLP Fundamentals",
    tutorName: "Dr. Emily Rodriguez",
    tutorPhoto: "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
    date: new Date(2025, 3, 18), // April 18, 2025
    startTime: "2:00 PM",
    endTime: "3:30 PM",
    location: "Room 112",
    type: "session",
    color: "bg-green-100 border-green-300 text-green-800",
    icon: <BookText className="h-4 w-4 text-green-500" />
  },
  {
    id: 3,
    title: "Science Test",
    tutorName: "Prof. Michael Chen",
    tutorPhoto: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
    date: new Date(2025, 3, 16), // April 16, 2025
    startTime: "11:00 AM",
    endTime: "12:00 PM",
    location: "Room 112",
    type: "test",
    color: "bg-purple-100 border-purple-300 text-purple-800",
    icon: <Beaker className="h-4 w-4 text-purple-500" />
  },
  {
    id: 4,
    title: "Project Build",
    tutorName: "Dr. Lisa Park",
    tutorPhoto: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
    date: new Date(2025, 3, 17), // April 17, 2025
    startTime: "2:00 PM",
    endTime: "3:00 PM",
    location: "Room 112",
    type: "project",
    color: "bg-yellow-100 border-yellow-300 text-yellow-800",
    icon: <Pencil className="h-4 w-4 text-yellow-500" />
  },
  {
    id: 5,
    title: "Extra Class",
    tutorName: "Dr. Sarah Johnson",
    tutorPhoto: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
    date: new Date(2025, 3, 19), // April 19, 2025
    startTime: "9:00 AM",
    endTime: "10:00 AM",
    location: "Online",
    type: "session",
    color: "bg-blue-100 border-blue-300 text-blue-800",
    icon: <Laptop className="h-4 w-4 text-blue-500" />
  },
  {
    id: 6,
    title: "Guidance",
    tutorName: "Prof. Michael Chen",
    tutorPhoto: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
    date: new Date(2025, 3, 17), // April 17, 2025
    startTime: "12:00 PM",
    endTime: "1:00 PM",
    location: "Room 70",
    type: "guidance",
    color: "bg-indigo-100 border-indigo-300 text-indigo-800",
    icon: <Brain className="h-4 w-4 text-indigo-500" />
  },
  {
    id: 7,
    title: "Vocabulary",
    tutorName: "Dr. Lisa Park",
    tutorPhoto: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
    date: new Date(2025, 3, 16), // April 16, 2025
    startTime: "1:00 PM",
    endTime: "2:00 PM",
    location: "Hall 211",
    type: "session",
    color: "bg-pink-100 border-pink-300 text-pink-800",
    icon: <BookText className="h-4 w-4 text-pink-500" />
  },
];

const Dashboard = () => {
  const { user, userType, loading, isInitialized } = useAuth();
  const profileData = useProfileData();
  const navigate = useNavigate();

  // Use sessions hook for real data
  const {
    upcomingSessions,
    pastSessions,
    sessionStats,
    isLoading: isLoadingSessions,
    error: sessionsError,
    refreshSessions
  } = useSessions();

  // Use Zustand store for all state
  const {
    selectedDate,
    showWelcomeMessage,
    currentMonth,
    calendarView,
    setActiveTab,
    setSelectedDate,
    setShowWelcomeMessage,
    setCalendarView,
    nextMonth,
    prevMonth,
  } = useStudentDashboardStore();

  // Use billing store
  const {
    invoices,
    fetchSubscriptions,
    fetchInvoices,
    fetchActiveSubscriptions,
    activeSubscriptions
  } = useBillingStore();

  useEffect(() => {
    const checkUserType = async () => {
      if (loading || !isInitialized) return;

      // Use the userType from AuthContext instead of querying Supabase directly
      if (userType === "admin") {
        console.log("User is admin, redirecting to admin dashboard");
        navigate("/admin-dashboard");
      }
    };

    checkUserType();
  }, [userType, loading, isInitialized, navigate]);

  // Fetch billing data when user is available
  useEffect(() => {
    if (user?.id) {
      fetchSubscriptions(user.id);
      fetchInvoices(user.id);
      fetchActiveSubscriptions(user.id);
    }
  }, [fetchSubscriptions, fetchInvoices, fetchActiveSubscriptions, user?.id]);

  // Show loading state while auth is initializing
  if (loading || !isInitialized) {
    return (
      <div className="flex h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <LoadingSpinner />
          <p className="mt-4 text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  // Create a notification dropdown for the actions slot
  const notificationActions = <NotificationDropdown />;

  return (
    <StudentPageLayout
      title={`Welcome, ${profileData.displayName || user?.email?.split("@")[0] || "Student"}!`}
      description="Manage your tutoring sessions and view your learning progress."
      actions={notificationActions}
    >
      {showWelcomeMessage && (
        <div className="bg-gradient-to-r from-blue-100 to-indigo-100 rounded-lg shadow-md p-6 mb-6 border border-blue-200">
          <h2 className="text-xl font-semibold text-blue-800 mb-2">
            Welcome to your learning dashboard!
          </h2>
          <p className="text-gray-700">
            Your student profile has been set up successfully. You're now
            ready to start your learning journey!
          </p>
          <button
            onClick={() => setShowWelcomeMessage(false)}
            className="mt-3 text-sm text-blue-600 hover:text-blue-800"
          >
            Dismiss
          </button>
        </div>
      )}



      <Tabs
        defaultValue="sessions"
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="sessions">Sessions</TabsTrigger>
          <TabsTrigger value="calendar">Calendar</TabsTrigger>
          <TabsTrigger value="stats">Stats</TabsTrigger>
        </TabsList>

        <TabsContent value="sessions">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Your Sessions
                {sessionsError && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={refreshSessions}
                    className="ml-2"
                  >
                    <RefreshCw className="h-4 w-4 mr-1" />
                    Retry
                  </Button>
                )}
              </CardTitle>
              <CardDescription>
                View and manage your upcoming and past sessions
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingSessions ? (
                <div className="flex items-center justify-center py-8">
                  <LoadingSpinner />
                  <span className="ml-2">Loading sessions...</span>
                </div>
              ) : sessionsError ? (
                <div className="text-center py-6">
                  <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                  <p className="text-red-600 mb-4">{sessionsError}</p>
                  <Button onClick={refreshSessions} variant="outline">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Try Again
                  </Button>
                </div>
              ) : upcomingSessions.length === 0 ? (
                <div className="text-center py-6">
                  <p className="text-gray-500 mb-4">
                    You don't have any upcoming sessions.
                  </p>
                  <Button asChild>
                    <Link to="/tutor-search">Find a Tutor</Link>
                  </Button>
                </div>
              ) : (
                <div className="space-y-6">
                  {upcomingSessions.map((session) => (
                    <SessionCard key={session.id} session={session} />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

                {/* Past Sessions */}
                <Card className="mt-6">
                  <CardHeader className="pb-3">
                    <CardTitle>Past Sessions</CardTitle>
                    <CardDescription>
                      Your completed tutoring sessions.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {isLoadingSessions ? (
                      <div className="flex items-center justify-center py-8">
                        <LoadingSpinner />
                        <span className="ml-2">Loading past sessions...</span>
                      </div>
                    ) : pastSessions.length === 0 ? (
                      <p className="text-gray-500 text-center py-6">
                        You haven't completed any sessions yet.
                      </p>
                    ) : (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Tutor</TableHead>
                            <TableHead>Date</TableHead>
                            <TableHead>Topic</TableHead>
                            <TableHead>Duration</TableHead>
                            <TableHead>Rating</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {pastSessions.map((session) => {
                            const { date, time } = formatSessionTime(session.scheduled_at);
                            const tutorDisplayName = getTutorDisplayName(session.tutor_profile, session.tutor_data);
                            const tutorPhoto = getTutorPhoto(session.tutor_profile);
                            const topicName = getTopicName(session.topic);
                            const rating = getSessionRating(session.session_feedback || []);
                            const tutorRating = getTutorRating(session.tutor_data);

                            return (
                              <TableRow key={session.id}>
                                <TableCell>
                                  <div className="flex items-center">
                                    <img
                                      src={tutorPhoto}
                                      alt={tutorDisplayName}
                                      className="w-8 h-8 rounded-full object-cover mr-2"
                                    />
                                    <div>
                                      <div className="flex items-center gap-1">
                                        <span className="font-medium">{tutorDisplayName}</span>
                                        {tutorRating && (
                                          <div className="flex items-center">
                                            <Star className="h-3 w-3 text-yellow-400 fill-yellow-400" />
                                            <span className="text-xs text-gray-600">{tutorRating}</span>
                                          </div>
                                        )}
                                      </div>
                                      {session.tutor_data?.education_level && (
                                        <p className="text-xs text-gray-500">{session.tutor_data.education_level}</p>
                                      )}
                                    </div>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  {date}
                                  <br />
                                  <span className="text-gray-500 text-sm">
                                    {time}
                                  </span>
                                </TableCell>
                                <TableCell>
                                  <Badge variant="outline">{topicName}</Badge>
                                  {session.batch?.name && (
                                    <p className="text-xs text-gray-500 mt-1">
                                      {session.batch.name}
                                    </p>
                                  )}
                                </TableCell>
                                <TableCell>{session.duration_min} min</TableCell>
                                <TableCell>
                                  {rating ? (
                                    <div className="flex items-center">
                                      <Star className="h-4 w-4 text-yellow-400 fill-yellow-400 mr-1" />
                                      <span>{rating}/5</span>
                                    </div>
                                  ) : (
                                    <span className="text-gray-400">No rating</span>
                                  )}
                                </TableCell>
                              </TableRow>
                            );
                          })}
                        </TableBody>
                      </Table>
                    )}
                  </CardContent>
                </Card>
        </TabsContent>

        <TabsContent value="calendar" className="space-y-8">
          <StudentCalendar
            calendarView={calendarView}
            selectedDate={selectedDate}
            currentMonth={currentMonth}
            calendarEvents={calendarEvents}
            setCalendarView={setCalendarView}
            setSelectedDate={setSelectedDate}
            nextPeriod={nextMonth}
            prevPeriod={prevMonth}
          />

          <UpcomingEvents calendarEvents={calendarEvents} />
        </TabsContent>

        <TabsContent value="stats" className="space-y-8">
          {isLoadingSessions ? (
            <div className="flex items-center justify-center py-8">
              <LoadingSpinner />
              <span className="ml-2">Loading statistics...</span>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Total Sessions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="text-4xl font-bold text-rfpurple-600">
                        {sessionStats?.totalSessions || 0}
                      </div>
                      <User className="h-12 w-12 text-rfpurple-200" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Total Hours</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="text-4xl font-bold text-rfpurple-600">
                        {sessionStats?.totalHours || 0}
                      </div>
                      <Clock className="h-12 w-12 text-rfpurple-200" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Average Rating</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="text-4xl font-bold text-rfpurple-600">
                        {sessionStats?.averageRating || 'N/A'}
                      </div>
                      <Star className="h-12 w-12 text-rfpurple-200" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Billing Summary */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Other dashboard cards can go here */}
                <BillingSummary
                  activeSubscriptions={activeSubscriptions}
                  recentInvoices={invoices.slice(0, 3)}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="col-span-1">
                  <CardHeader>
                    <CardTitle>Learning Focus</CardTitle>
                    <CardDescription>
                      Your most studied topics
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {sessionStats?.favoriteTopics && sessionStats.favoriteTopics.length > 0 ? (
                        sessionStats.favoriteTopics.map((topic: string, index: number) => (
                          <div key={topic} className="flex items-center">
                            <BookOpen className="h-5 w-5 mr-2 text-rfpurple-500" />
                            <span className="flex-grow">{topic}</span>
                            <Badge variant="outline">
                              {index === 0
                                ? "Most studied"
                                : index === 1
                                ? "2nd most"
                                : "3rd most"}
                            </Badge>
                          </div>
                        ))
                      ) : (
                        <p className="text-gray-500 text-center py-4">
                          No topics studied yet
                        </p>
                      )}
                    </div>
                  </CardContent>
                </Card>

                <Card className="col-span-1">
                  <CardHeader>
                    <CardTitle>Favorite Tutors</CardTitle>
                    <CardDescription>
                      Tutors you work with most often
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {sessionStats?.favoriteTutors && sessionStats.favoriteTutors.length > 0 ? (
                        sessionStats.favoriteTutors.map(
                          (instructor: string, index: number) => (
                            <div key={instructor} className="flex items-center">
                              <User className="h-5 w-5 mr-2 text-rfpurple-500" />
                              <span className="flex-grow">{instructor}</span>
                              <Badge variant="outline">
                                {index === 0 ? "Most sessions" : `${index + 1} sessions`}
                              </Badge>
                            </div>
                          )
                        )
                      ) : (
                        <p className="text-gray-500 text-center py-4">
                          No tutors worked with yet
                        </p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Learning Progress</CardTitle>
                  <CardDescription>
                    Your tutoring sessions over time
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px] flex items-center justify-center">
                    <div className="flex items-center flex-col text-center">
                      <BarChart className="h-16 w-16 text-gray-300 mb-4" />
                      <h3 className="text-lg font-medium">
                        Charts will be displayed here
                      </h3>
                      <p className="text-gray-500 mt-2">
                        Complete more sessions to see your learning progress
                        over time.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>
      </Tabs>
    </StudentPageLayout>
  );
};

export default Dashboard;
