import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/context/AuthContext';
import { sessionsService, SessionData, SessionStats } from '@/services/sessionsService';

interface UseSessionsReturn {
  // Data
  upcomingSessions: SessionData[];
  pastSessions: SessionData[];
  allSessions: SessionData[];
  sessionStats: SessionStats | null;
  
  // Loading states
  isLoading: boolean;
  isLoadingUpcoming: boolean;
  isLoadingPast: boolean;
  isLoadingStats: boolean;
  
  // Error states
  error: string | null;
  upcomingError: string | null;
  pastError: string | null;
  statsError: string | null;
  
  // Actions
  refreshSessions: () => Promise<void>;
  refreshUpcoming: () => Promise<void>;
  refreshPast: () => Promise<void>;
  refreshStats: () => Promise<void>;
  getSessionById: (sessionId: string) => Promise<SessionData | null>;
}

export const useSessions = (): UseSessionsReturn => {
  const { user } = useAuth();
  
  // Data state
  const [upcomingSessions, setUpcomingSessions] = useState<SessionData[]>([]);
  const [pastSessions, setPastSessions] = useState<SessionData[]>([]);
  const [allSessions, setAllSessions] = useState<SessionData[]>([]);
  const [sessionStats, setSessionStats] = useState<SessionStats | null>(null);
  
  // Loading states
  const [isLoadingUpcoming, setIsLoadingUpcoming] = useState(false);
  const [isLoadingPast, setIsLoadingPast] = useState(false);
  const [isLoadingStats, setIsLoadingStats] = useState(false);
  
  // Error states
  const [upcomingError, setUpcomingError] = useState<string | null>(null);
  const [pastError, setPastError] = useState<string | null>(null);
  const [statsError, setStatsError] = useState<string | null>(null);
  
  // Computed loading state
  const isLoading = isLoadingUpcoming || isLoadingPast || isLoadingStats;
  
  // Computed error state
  const error = upcomingError || pastError || statsError;

  /**
   * Fetch upcoming sessions
   */
  const fetchUpcomingSessions = useCallback(async () => {
    if (!user?.id) return;
    
    setIsLoadingUpcoming(true);
    setUpcomingError(null);
    
    try {
      const sessions = await sessionsService.fetchUpcomingSessions(user.id);
      setUpcomingSessions(sessions);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch upcoming sessions';
      setUpcomingError(errorMessage);
      console.error('Error fetching upcoming sessions:', err);
    } finally {
      setIsLoadingUpcoming(false);
    }
  }, [user?.id]);

  /**
   * Fetch past sessions
   */
  const fetchPastSessions = useCallback(async () => {
    if (!user?.id) return;
    
    setIsLoadingPast(true);
    setPastError(null);
    
    try {
      const sessions = await sessionsService.fetchPastSessions(user.id);
      setPastSessions(sessions);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch past sessions';
      setPastError(errorMessage);
      console.error('Error fetching past sessions:', err);
    } finally {
      setIsLoadingPast(false);
    }
  }, [user?.id]);

  /**
   * Fetch all sessions
   */
  const fetchAllSessions = useCallback(async () => {
    if (!user?.id) return;
    
    try {
      const sessions = await sessionsService.fetchStudentSessions(user.id);
      setAllSessions(sessions);
    } catch (err) {
      console.error('Error fetching all sessions:', err);
    }
  }, [user?.id]);

  /**
   * Calculate session statistics
   */
  const fetchSessionStats = useCallback(async () => {
    if (!user?.id) return;
    
    setIsLoadingStats(true);
    setStatsError(null);
    
    try {
      const stats = await sessionsService.calculateSessionStats(user.id);
      setSessionStats(stats);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to calculate session statistics';
      setStatsError(errorMessage);
      console.error('Error calculating session stats:', err);
    } finally {
      setIsLoadingStats(false);
    }
  }, [user?.id]);

  /**
   * Refresh all session data
   */
  const refreshSessions = useCallback(async () => {
    await Promise.all([
      fetchUpcomingSessions(),
      fetchPastSessions(),
      fetchAllSessions(),
      fetchSessionStats()
    ]);
  }, [fetchUpcomingSessions, fetchPastSessions, fetchAllSessions, fetchSessionStats]);

  /**
   * Get session by ID
   */
  const getSessionById = useCallback(async (sessionId: string): Promise<SessionData | null> => {
    try {
      return await sessionsService.getSessionById(sessionId);
    } catch (err) {
      console.error('Error fetching session by ID:', err);
      return null;
    }
  }, []);

  // Initial data fetch
  useEffect(() => {
    if (user?.id) {
      refreshSessions();
    }
  }, [user?.id, refreshSessions]);

  return {
    // Data
    upcomingSessions,
    pastSessions,
    allSessions,
    sessionStats,
    
    // Loading states
    isLoading,
    isLoadingUpcoming,
    isLoadingPast,
    isLoadingStats,
    
    // Error states
    error,
    upcomingError,
    pastError,
    statsError,
    
    // Actions
    refreshSessions,
    refreshUpcoming: fetchUpcomingSessions,
    refreshPast: fetchPastSessions,
    refreshStats: fetchSessionStats,
    getSessionById
  };
};
