-- =====================================================
-- MICROSOFT TEAMS INTEGRATION CLEANUP SCRIPT
-- =====================================================
-- Use this script to remove demo data after testing
-- Run these queries in order to maintain referential integrity

-- =====================================================
-- 1. REMOVE MEETING-RELATED DATA
-- =====================================================

-- Remove meeting events (if any were created)
DELETE FROM meeting_events 
WHERE meeting_session_id = 'demo-meeting-001';

-- Remove meeting participants
DELETE FROM meeting_participants 
WHERE meeting_session_id = 'demo-meeting-001';

-- Remove meeting sessions
DELETE FROM meeting_sessions 
WHERE session_id = 'demo-session-001';

-- =====================================================
-- 2. REMOVE SESSION DATA
-- =====================================================

-- Remove session feedback (if any)
DELETE FROM session_feedback 
WHERE session_id = 'demo-session-001';

-- Remove session details (if any)
DELETE FROM session_details 
WHERE session_id = 'demo-session-001';

-- Remove sessions
DELETE FROM sessions 
WHERE id = 'demo-session-001';

-- =====================================================
-- 3. REMOVE USER INTEGRATION DATA
-- =====================================================

-- Remove user meeting preferences
DELETE FROM user_meeting_preferences 
WHERE user_id IN ('demo-tutor-001', 'demo-student-001', 'demo-admin-001');

-- Remove user integrations (OAuth tokens)
DELETE FROM user_integrations 
WHERE user_id IN ('demo-tutor-001', 'demo-student-001', 'demo-admin-001');

-- =====================================================
-- 4. REMOVE BATCH AND CURRICULUM DATA
-- =====================================================

-- Remove batch subtopics (if any)
DELETE FROM batch_subtopics 
WHERE batch_topic_id IN (
    SELECT id FROM batch_topics WHERE batch_id = 'demo-batch-001'
);

-- Remove batch topics
DELETE FROM batch_topics 
WHERE batch_id = 'demo-batch-001';

-- Remove batches
DELETE FROM batches 
WHERE id = 'demo-batch-001';

-- =====================================================
-- 5. REMOVE CURRICULUM DATA
-- =====================================================

-- Remove resources (if any)
DELETE FROM resources 
WHERE subtopic_id = 'demo-subtopic-limits';

-- Remove subtopics
DELETE FROM subtopics 
WHERE id = 'demo-subtopic-limits';

-- Remove topics
DELETE FROM topics 
WHERE id = 'demo-topic-calculus';

-- Remove subjects
DELETE FROM subjects 
WHERE id = 'demo-subject-math';

-- =====================================================
-- 6. REMOVE USER PROFILES
-- =====================================================

-- Remove profiles (this will cascade to auth.users if configured)
DELETE FROM profiles 
WHERE id IN ('demo-student-001', 'demo-tutor-001', 'demo-admin-001');

-- =====================================================
-- 7. OPTIONAL: REMOVE MEETING PROVIDER
-- =====================================================
-- Note: Only remove if you don't want to keep Microsoft Teams as a provider
-- Uncomment the following line if you want to remove the Teams provider entirely

-- DELETE FROM meeting_providers WHERE name = 'microsoft_teams';

-- =====================================================
-- 8. VERIFICATION QUERIES
-- =====================================================

-- Verify cleanup was successful
SELECT 'Demo profiles remaining' as check_type, COUNT(*) as count
FROM profiles 
WHERE id IN ('demo-student-001', 'demo-tutor-001', 'demo-admin-001')

UNION ALL

SELECT 'Demo sessions remaining' as check_type, COUNT(*) as count
FROM sessions 
WHERE id = 'demo-session-001'

UNION ALL

SELECT 'Demo meeting sessions remaining' as check_type, COUNT(*) as count
FROM meeting_sessions 
WHERE session_id = 'demo-session-001'

UNION ALL

SELECT 'Demo user integrations remaining' as check_type, COUNT(*) as count
FROM user_integrations 
WHERE user_id IN ('demo-tutor-001', 'demo-student-001', 'demo-admin-001')

UNION ALL

SELECT 'Demo batches remaining' as check_type, COUNT(*) as count
FROM batches 
WHERE id = 'demo-batch-001'

UNION ALL

SELECT 'Demo subjects remaining' as check_type, COUNT(*) as count
FROM subjects 
WHERE id = 'demo-subject-math';

-- =====================================================
-- 9. RESET AUTO-INCREMENT SEQUENCES (if needed)
-- =====================================================
-- Note: This is typically not needed for UUID-based tables
-- but included for completeness

-- If you have any auto-increment fields that need resetting, add them here
-- Example:
-- SELECT setval('table_name_id_seq', (SELECT MAX(id) FROM table_name));

-- =====================================================
-- 10. CLEANUP CONFIRMATION
-- =====================================================

-- Final confirmation that demo data has been removed
DO $$
DECLARE
    demo_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO demo_count
    FROM (
        SELECT id FROM profiles WHERE id LIKE 'demo-%'
        UNION ALL
        SELECT id FROM sessions WHERE id LIKE 'demo-%'
        UNION ALL
        SELECT id FROM batches WHERE id LIKE 'demo-%'
        UNION ALL
        SELECT id FROM subjects WHERE id LIKE 'demo-%'
    ) demo_records;
    
    IF demo_count = 0 THEN
        RAISE NOTICE 'SUCCESS: All demo data has been successfully removed.';
    ELSE
        RAISE NOTICE 'WARNING: % demo records still remain. Please check manually.', demo_count;
    END IF;
END $$;
