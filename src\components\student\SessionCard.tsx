import React from "react";
import { Badge } from "@/components/ui/Badge";
import { Button } from "@/components/ui/Button";
import {
  Clock,
  Calendar as CalendarIcon,
  Video,
  Edit2,
  Star,
  X,
} from "lucide-react";
import { Link } from "react-router-dom";

// Helper function to format session time
const formatSessionTime = (scheduledAt: string) => {
  const date = new Date(scheduledAt);
  return {
    date: date.toLocaleDateString(),
    time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  };
};

// Helper function to get tutor name
const getTutorName = (tutorProfile: any) => {
  if (!tutorProfile) return 'Unknown Tutor';
  return `${tutorProfile.first_name || ''} ${tutorProfile.last_name || ''}`.trim() || 'Unknown Tutor';
};

// Helper function to get tutor display name with credentials
const getTutorDisplayName = (tutorProfile: any, tutorData: any) => {
  const baseName = getTutorName(tutorProfile);
  if (baseName === 'Unknown Tutor') return baseName;

  // Add education level if available
  if (tutorData?.education_level) {
    const educationPrefix = tutorData.education_level.toLowerCase().includes('phd') ? 'Dr.' :
                           tutorData.education_level.toLowerCase().includes('master') ? 'Prof.' : '';
    if (educationPrefix) {
      return `${educationPrefix} ${baseName}`;
    }
  }

  return baseName;
};

// Helper function to get topic name
const getTopicName = (topic: any, subtopic: any) => {
  if (subtopic?.name) return subtopic.name;
  if (topic?.name) return topic.name;
  return 'Unknown Topic';
};

// Helper function to get tutor photo
const getTutorPhoto = (tutorProfile: any) => {
  return tutorProfile?.profile_picture_url ||
    "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80";
};

// Helper function to get tutor rating
const getTutorRating = (tutorData: any) => {
  return tutorData?.rating || null;
};

// Helper function to generate human-readable session ID
const generateSessionId = (sessionUuid: string) => {
  // Create a simple hash from UUID to generate consistent session numbers
  let hash = 0;
  for (let i = 0; i < sessionUuid.length; i++) {
    const char = sessionUuid.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  // Ensure positive number and add base offset
  const sessionNumber = Math.abs(hash % 9000) + 1000;
  return `S-${sessionNumber}`;
};

// Helper function to get subject name from topic
const getSubjectName = (topic: any) => {
  // Use the subject data from the topic if available, otherwise fallback to topic name
  return topic?.subject?.name || topic?.name || 'Unknown Subject';
};

interface SessionCardProps {
  session: any;
  showActions?: boolean;
  showSessionId?: boolean;
  onReschedule?: (session: any) => void;
  onJoin?: (session: any) => void;
  onCancel?: (session: any) => void;
}

const SessionCard: React.FC<SessionCardProps> = ({
  session,
  showActions = true,
  showSessionId = false,
  onReschedule,
  onJoin,
  onCancel
}) => {
  const { date, time } = formatSessionTime(session.scheduled_at);
  const tutorDisplayName = getTutorDisplayName(session.tutor_profile, session.tutor_data);
  const tutorPhoto = getTutorPhoto(session.tutor_profile);
  const topicName = getTopicName(session.topic, session.subtopic);
  const tutorRating = getTutorRating(session.tutor_data);
  const sessionId = generateSessionId(session.id);

  const handleReschedule = () => {
    if (onReschedule) {
      onReschedule(session);
    }
  };

  const handleJoin = () => {
    if (onJoin) {
      onJoin(session);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel(session);
    }
  };

  return (
    <div className="p-4 border rounded-lg bg-white">
      {showSessionId && (
        <div className="mb-2">
          <Badge variant="outline" className="text-xs">
            {sessionId}
          </Badge>
        </div>
      )}
      <div className="flex flex-col md:flex-row md:items-center">
        <div className="flex items-center mb-4 md:mb-0 md:mr-8">
          <img
            src={tutorPhoto}
            alt={tutorDisplayName}
            className="w-14 h-14 rounded-full object-cover mr-4"
          />
          <div>
            <div className="flex items-center gap-2">
              <h3 className="font-medium">{tutorDisplayName}</h3>
              {tutorRating && (
                <div className="flex items-center">
                  <Star className="h-3 w-3 text-yellow-400 fill-yellow-400" />
                  <span className="text-xs text-gray-600 ml-1">{tutorRating}</span>
                </div>
              )}
            </div>
            <Badge className="mt-1">{topicName}</Badge>
            {session.batch?.name && (
              <p className="text-sm text-gray-500 mt-1">
                {session.batch.name}
              </p>
            )}
            {session.tutor_data?.subjects_taught && (
              <p className="text-xs text-gray-400 mt-1">
                Specializes in: {session.tutor_data.subjects_taught}
              </p>
            )}
          </div>
        </div>

        <div className="flex-grow space-y-1 md:flex md:items-center md:space-y-0 md:space-x-6">
          <div className="flex items-center">
            <CalendarIcon className="h-4 w-4 mr-1 text-gray-500" />
            <span>{date}</span>
          </div>
          <div className="flex items-center">
            <Clock className="h-4 w-4 mr-1 text-gray-500" />
            <span>{time} ({session.duration_min} min)</span>
          </div>
          <div className="flex items-center">
            <Badge
              variant={session.status === 'scheduled' ? 'default' : 'secondary'}
              className="capitalize"
            >
              {session.status}
            </Badge>
          </div>
        </div>

        {showActions && (
          <div className="mt-4 md:mt-0 md:ml-6 flex justify-between md:justify-end md:space-x-2">
            {onReschedule && (
              <Button
                variant="outline"
                size="sm"
                className="mr-2"
                onClick={handleReschedule}
              >
                <Edit2 className="h-4 w-4 mr-1" />
                Reschedule
              </Button>
            )}
            {onJoin && (
              <Button
                size="sm"
                className="button-gradient mr-2"
                onClick={handleJoin}
              >
                <Video className="h-4 w-4 mr-1" />
                Join
              </Button>
            )}
            {onCancel && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleCancel}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <X className="h-4 w-4 mr-1" />
                Cancel
              </Button>
            )}
            {/* Fallback to Link-based actions if no handlers provided */}
            {!onReschedule && !onJoin && !onCancel && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  className="mr-2"
                  asChild
                >
                  <Link to={`/edit-session/${session.id}`}>
                    <Edit2 className="h-4 w-4 mr-1" />
                    Reschedule
                  </Link>
                </Button>
                <Button
                  size="sm"
                  className="button-gradient"
                  asChild
                >
                  <Link to={`/join-session/${session.id}`}>
                    <Video className="h-4 w-4 mr-1" />
                    Join
                  </Link>
                </Button>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default SessionCard;
export { formatSessionTime, getTutorDisplayName, getTopicName, getTutorPhoto, getTutorRating, generateSessionId, getSubjectName };
