-- =====================================================
-- MICROSOFT TEAMS INTEGRATION DEMO DATA
-- =====================================================
-- This file contains demo insert queries for testing the Microsoft Teams integration
-- Run these queries to create sample data for testing meeting creation and joining

-- =====================================================
-- 1. DEMO USERS AND PROFILES
-- =====================================================

-- Insert demo profiles (assuming auth.users already exist)
-- not needed
INSERT INTO profiles (id, first_name, last_name, user_type, email, timezone) VALUES
('demo-student-001', 'Emma', 'Wilson', 'student', '<EMAIL>', 'America/New_York'),
('demo-tutor-001', 'Dr. Sarah', '<PERSON>', 'tutor', '<EMAIL>', 'America/Los_Angeles'),
('demo-admin-001', 'Michael', 'Chen', 'admin', '<EMAIL>', 'America/Chicago')
ON CONFLICT (id) DO UPDATE SET
  first_name = EXCLUDED.first_name,
  last_name = EXCLUDED.last_name,
  email = EXCLUDED.email,
  timezone = EXCLUDED.timezone;

-- =====================================================
-- 2. DEMO CURRICULUM DATA
-- =====================================================

-- Insert demo subject
-- not needed
INSERT INTO subjects (id, name, description, icon) VALUES
('demo-subject-math', 'Mathematics', 'Advanced Mathematics for High School', '📐')
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description;

-- Insert demo topic
-- not needed
INSERT INTO topics (id, subject_id, name, description, display_order) VALUES
('demo-topic-calculus', 'demo-subject-math', 'Calculus I', 'Introduction to Differential and Integral Calculus', 1)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description;

-- Insert demo subtopic
-- not needed
INSERT INTO subtopics (id, topic_id, name, description, state_standard, display_order) VALUES
('demo-subtopic-limits', 'demo-topic-calculus', 'Limits and Continuity', 'Understanding limits and continuous functions', 'MATH.CALC.1', 1)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description;

-- =====================================================
-- 3. DEMO BATCH AND SUBSCRIPTION
-- =====================================================

-- Insert demo batch
INSERT INTO batches (id, name, student_id, product_type, product_name, default_tutor_id, status, start_date, end_date, total_sessions, remaining_sessions) VALUES
('demo-batch-001', 'Emma''s Mathematics Package', 'demo-student-001', 'booster', 'High Dosage Tutoring', 'demo-tutor-001', 'active',
 CURRENT_DATE, CURRENT_DATE + INTERVAL '3 months', 20, 18)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  status = EXCLUDED.status,
  remaining_sessions = EXCLUDED.remaining_sessions;

-- Insert batch topic
INSERT INTO batch_topics (id, batch_id, topic_id, custom_tutor_id, status) VALUES
('demo-batch-topic-001', 'demo-batch-001', 'demo-topic-calculus', NULL, 'in_progress')
ON CONFLICT (id) DO UPDATE SET
  status = EXCLUDED.status;

-- =====================================================
-- 4. MICROSOFT TEAMS INTEGRATION DATA
-- =====================================================

-- Insert Microsoft Teams provider (if not exists)
-- not needed
INSERT INTO meeting_providers (
    id,
    name,
    display_name,
    provider_type,
    is_active,
    is_default,
    supports_recording,
    supports_screen_sharing,
    supports_whiteboard,
    supports_chat,
    max_participants,
    default_settings
) VALUES (
    'provider-teams-001',
    'microsoft_teams',
    'Microsoft Teams',
    'third_party',
    true,
    true,
    true,
    true,
    true,
    true,
    300,
    '{
        "allowAnonymousUsers": false,
        "recordAutomatically": false,
        "lobbyBypassSettings": "organizationAndFederated",
        "allowMeetingChat": true,
        "allowTeamsCameraOn": true,
        "allowTeamsMicOn": true
    }'::jsonb
) ON CONFLICT (name) DO UPDATE SET
    is_active = EXCLUDED.is_active,
    is_default = EXCLUDED.is_default;

-- Insert demo user integration (OAuth tokens for tutor)
INSERT INTO user_integrations (
    user_id,
    provider,
    access_token,
    refresh_token,
    expires_at,
    scope
) VALUES (
    '47c5ddcb-cb5d-42ca-81d1-2baac9fb5832',
    'microsoft_teams',
    'demo_access_token_' || extract(epoch from now())::text,
    'demo_refresh_token_' || extract(epoch from now())::text,
    NOW() + INTERVAL '1 hour',
    'https://graph.microsoft.com/OnlineMeetings.ReadWrite https://graph.microsoft.com/User.Read'
) ON CONFLICT (user_id, provider) DO UPDATE SET
    access_token = EXCLUDED.access_token,
    expires_at = EXCLUDED.expires_at;

-- =====================================================
-- 5. DEMO SESSIONS WITH TEAMS MEETINGS
-- =====================================================

-- Insert demo session
INSERT INTO sessions (
    batch_id,
    topic_id,
    subtopic_id,
    tutor_id,
    student_id,
    scheduled_at,
    duration_min,
    status,
    mode,
    session_type,
    meeting_url,
    meeting_provider_id,
    created_by
) VALUES (
    '57574c66-9d18-47b2-a4f5-25c2f9662cf8',
    'eb74a152-311a-4515-972b-943070582efa',
    'a7efbe1c-2ccf-4d84-8a14-a8bd036cb4b0',
    '47c5ddcb-cb5d-42ca-81d1-2baac9fb5832',
    '715d2b84-cc4a-443b-bee1-74e80725b21d',
    NOW() + INTERVAL '1 day',
    60,
    'scheduled',
    'video',
    'regular',
    'https://teams.microsoft.com/l/meetup-join/demo-meeting-url',
    '222105b9-2ced-4456-a0f6-48bb9c0c8ca6',
    'tutor'
) ON CONFLICT (id) DO UPDATE SET
    scheduled_at = EXCLUDED.scheduled_at,
    meeting_url = EXCLUDED.meeting_url;

-- Insert demo meeting session
INSERT INTO meeting_sessions (
    session_id,
    provider_id,
    provider_meeting_id,
    meeting_url,
    join_url,
    meeting_settings,
    meeting_status,
    scheduled_start_time,
    scheduled_end_time,
    recording_enabled,
    provider_response
) VALUES (
    '50463678-6f0c-4d2c-b2e1-3f203bf94b0d',
    '222105b9-2ced-4456-a0f6-48bb9c0c8ca6',
    'demo_teams_meeting' || extract(epoch from now())::text,
    'https://teams.microsoft.com/l/meetup-join/demo-meeting-url',
    'https://teams.microsoft.com/l/meetup-join/demo-meeting-url',
    '{
        "allowAnonymousUsers": false,
        "recordAutomatically": false,
        "allowMeetingChat": true,
        "allowTeamsCameraOn": true,
        "allowTeamsMicOn": true
    }'::jsonb,
    'scheduled',
    NOW() + INTERVAL '1 day',
    NOW() + INTERVAL '1 day' + INTERVAL '1 hour',
    true,
    jsonb_build_object(
        'id', 'demo_teams_meeting_id',
        'joinWebUrl', 'https://teams.microsoft.com/l/meetup-join/demo-meeting-url',
        'subject', 'Mathematics - Numbers up to 99',
        'creationDateTime', NOW()
    )
) ON CONFLICT (session_id) DO UPDATE SET
    meeting_url = EXCLUDED.meeting_url,
    scheduled_start_time = EXCLUDED.scheduled_start_time;

-- =====================================================
-- 6. DEMO MEETING PARTICIPANTS
-- =====================================================

-- Insert demo meeting participants
INSERT INTO meeting_participants (
    meeting_session_id,
    user_id,
    participant_role,
    join_method
) VALUES
(
    '1c3d4610-ea36-4ec1-acdf-fc3766bf6965',
    '47c5ddcb-cb5d-42ca-81d1-2baac9fb5832',
    'host',
    'web'
),
(
    '1c3d4610-ea36-4ec1-acdf-fc3766bf6965',
    '715d2b84-cc4a-443b-bee1-74e80725b21d',
    'participant',
    'web'
) ON CONFLICT (id) DO UPDATE SET
    participant_role = EXCLUDED.participant_role;

-- =====================================================
-- 7. DEMO USER MEETING PREFERENCES
-- =====================================================

-- Insert demo user meeting preferences
-- not needed, this functionality is not needed
INSERT INTO user_meeting_preferences (
    id,
    user_id,
    preferred_provider_id,
    provider_settings,
    auto_join_enabled,
    recording_preference
) VALUES (
    'demo-pref-001',
    'demo-tutor-001',
    'provider-teams-001',
    '{
        "defaultRecording": false,
        "defaultMute": false,
        "defaultCamera": true
    }'::jsonb,
    true,
    'ask'
) ON CONFLICT (user_id, preferred_provider_id) DO UPDATE SET
    auto_join_enabled = EXCLUDED.auto_join_enabled;
